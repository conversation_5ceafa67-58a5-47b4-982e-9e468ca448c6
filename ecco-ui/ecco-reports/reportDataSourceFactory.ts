import * as types from "@eccosolutions/ecco-common";
import {EccoDate, EccoDateTime, NumberToObjectMap} from "@eccosolutions/ecco-common";
import {apiClient} from "ecco-components";
import * as sessionDataDomain from "ecco-dto";
import {
    AddressedLocationAjaxRepository,
    AddressHistoryDto,
    BaseOutcomeBasedWork,
    Building,
    OccupancyDto,
    CalendarAjaxRepository,
    ClientAjaxRepository,
    ContactsAjaxRepository,
    EvidenceGroup,
    QuestionnaireAnswersSnapshotDto,
    QuestionnaireWorkAjaxRepository,
    QuestionnaireWorkDto,
    ReferralAjaxRepository,
    ReferralDto,
    ReferralSummaryDto,
    RiskEvidenceAjaxRepository,
    RiskGroupEvidenceDto,
    ServiceRecipientAssociatedContact,
    ServiceType,
    SessionDataAjaxRepository,
    SnapshotPeriod,
    StaffDto,
    SupportWorkAjaxRepository,
    WorkersAjaxRepository,
    ServiceRecipientAjaxRepository,
    DemandScheduleDto,
    FinanceReceiptDto,
    FinanceChargeDto,
    SelectionCriteriaDto,
    SupportFlags,
    RepairDto,
    EvidenceFlags,
    BuildingRepository,
    ReferralRepository,
    ServiceRecipientRepository
} from "ecco-dto";
import {Agreement, RotaAjaxRepository, ServiceRecipientDemandDto} from "ecco-rota";
import {Client} from "ecco-dto";
import {Agency, Individual} from "ecco-dto";
import * as evidenceDto from "ecco-dto";
import {
    BaseActionInstanceSnapshotDto,
    BaseServiceRecipientCommandDto,
    FormEvidence,
    TaskStatus
} from "ecco-dto";
import * as riskDto from "ecco-dto";
import {RiskFlags, RiskWorkEvidenceDto} from "ecco-dto";
import * as groupSupportDtos from "ecco-dto";
import {ReportCriteriaDto} from "ecco-dto";
import {Review} from "ecco-dto";
import {User} from "ecco-dto";
import {ServiceRecipient} from "ecco-dto";
import {SingleValueHistoryDto} from "ecco-dto";

import {AclAjaxRepository} from "ecco-dto";
import {CompanyAjaxRepository} from "ecco-dto";
import {SingleValueHistoryAjaxRepository} from "ecco-dto";
import {ClientAttendance} from "ecco-dto";
import {GroupSupportAjaxRepository} from "ecco-dto";
import {GroupSupportActivityTypeAjaxRepository} from "ecco-dto";
import {HactAjaxRepository} from "ecco-dto";
import {QuestionGroupAjaxRepository} from "ecco-dto";
import {isIndividual} from "ecco-dto";

import {getBuildingRepository, getFormEvidenceRepository} from "ecco-offline-data";
import {ActivityAttendanceOnlyAnalysis} from "./analysis/activityAnalysis";
import {ServiceRecipientCommandOnlyAnalysis} from "./analysis/commandAnalysis";
import {
    ByMonthAnalysis,
    GroupedWorkAnalysis,
    WorkAnalysisGroupSummary
} from "./analysis/groupedSummaryCommonAnalysis";
import {
    QnAnswerAnalysis,
    QuestionnaireSnapshotOnlyAnalysis
} from "./analysis/questionnaireAnalysis";
import {
    AddressHistoryAnalysis,
    AgencyAnalysis,
    AssociatedContactsAnalysis,
    ClientAnalysis,
    ProfessionalAnalysis,
    ReferralAggregateAnalysis,
    ReviewAnalysis
} from "./analysis/referralAnalysis";
import {
    RotaAgreementAnalysis,
    RotaDemandAnalysis,
    RotaScheduleAnalysis
} from "./analysis/rotaAnalysis";
import {TaskAnalysis} from "./analysis/taskAnalysis";
import {CountsByMonthDto, extractPair, ReferralAggregate, SequenceAnalysis} from "./analysis/types";
import {UserOnlyAnalysis} from "./analysis/userAnalysis";
import {
    CustomFormWorkOnlyAnalysis,
    RiskFlagsAnalysis,
    RiskRatingsAnalysis,
    RiskWorkOnlyAnalysis,
    SupportWorkOnlyAnalysis,
    EvidenceFlagsAnalysis,
    WorkAnyAnalysis,
} from "./analysis/workAnalysis";
import {ReportAjaxRepository} from "./ReportAjaxRepository";
import {AnalyserReportStage, AnalysisContext, DataSource, SelectionCriteria} from "./chart-domain";
import {SmartStepsSnapshotOnlyAnalysis} from "./analysis/smartStepAnalysis";
import {ServiceTypeAnalysis} from "./analysis/serviceConfigAnalysis";
import {BuildingOnlyAnalysis, RepairOnlyAnalysis} from "./analysis/buildingAnalysis";
import {sequentialMapAll} from "ecco-commands";
import {ReportStageDto} from "ecco-dto";
import Lazy = require("lazy");

import workCommonAnalysis = require("./analysis/workCommonAnalysis");
import domain = require("./chart-domain");
import {SupportWork} from "ecco-dto";
import RiskWork = riskDto.RiskWorkEvidenceDto;
import HactSessionData = sessionDataDomain.HactSessionData;
import SessionData = sessionDataDomain.SessionData;
import {
    FinanceChargeAnalysis,
    financeCombined,
    FinanceReceiptOnlyAnalysis,
    OccupancyAnalysis
} from "./analysis/financeAnalysis";
import {CalendarAnalysis, CalendarEventWithParent} from "./analysis/calendarAnalysis";
import {groupByNativeNumber, groupByNativeString} from "ecco-dto";
import Sequence = LazyJS.Sequence;
import {FinanceAjaxRepository} from "ecco-dto";

const reportApiClient = apiClient.withCachePeriod(300);
const reportWebApiClient = apiClient.withCachePeriod(300);
const addressRepository = new AddressedLocationAjaxRepository(reportApiClient);
const calendarRepository = new CalendarAjaxRepository(reportWebApiClient);
const clientRepository = new ClientAjaxRepository(reportApiClient);
const contactRepository = new ContactsAjaxRepository(reportApiClient);
const questionnaireWorkRepository = new QuestionnaireWorkAjaxRepository(reportApiClient);
const financeRepository = new FinanceAjaxRepository(reportApiClient);
const referralRepository = new ReferralAjaxRepository(reportApiClient);
const riskWorkRepository = new RiskEvidenceAjaxRepository(reportApiClient);
const supportWorkRepository = new SupportWorkAjaxRepository(reportWebApiClient);
const sessionDataRepository = new SessionDataAjaxRepository(reportApiClient);
const workersRepository = new WorkersAjaxRepository(reportApiClient);
const serviceRecipientRepository = new ServiceRecipientAjaxRepository(reportApiClient);
const activityTypeRepository = new GroupSupportActivityTypeAjaxRepository(reportApiClient);
const aclRepository = new AclAjaxRepository(reportApiClient);
const companyRepository = new CompanyAjaxRepository(reportApiClient);
const groupSupportRepository = new GroupSupportAjaxRepository(reportApiClient);
const hactRepository = new HactAjaxRepository(reportApiClient);
const questionGroupRepository = new QuestionGroupAjaxRepository(reportApiClient);
const rotaRepository = new RotaAjaxRepository(reportApiClient, () =>
    sessionDataRepository.getSessionData()
);
const singleValueHistoryRepository = new SingleValueHistoryAjaxRepository(reportApiClient);

function isNumber(str: string) {
    // Abuse isFinite to eliminate groupBy having created keys "undefined" or "null"
    // [1,null,"null",undefined,"undefined","1"].filter(isFinite)
    // (3) [1, null, "1"]
    return isFinite(str as any as number);
}

export class ReportDataSourceFactory {
    public static getDataSource(chartDef: domain.ChartDefinition): DataSource<any> | null {
        let aggregator = new ReportDataSourceAggregatorDefault(chartDef);
        return aggregator.getDataSource();
    }
}

export class ReportDataSourceAggregatorDefault {
    constructor(private chartDef: domain.ChartDefinition) {}

    public getDataSource(): DataSource<any> | null {
        return this.joinDataSource(0, this.chartDef.getAllSelectionCriteria().length - 1, null);
    }

    private joinDataSource(
        selectionCriteriaIndex: number,
        selectionCriteriaIndexMax: number,
        cumulativeDataSource: DataSource<any> | null
    ): DataSource<any> | null {
        if (selectionCriteriaIndex <= selectionCriteriaIndexMax) {
            // clone the current selection criteria as there is potential for manipulation
            let currentSelectionCriteria = this.chartDef
                .getSelectionCriteria(selectionCriteriaIndex)
                .clone();

            // the first selectionCriteria we just need to iterate
            if (selectionCriteriaIndex == 0) {
                let currentDataSource = this.getSingleDataSource(currentSelectionCriteria, null);
                return this.joinDataSource(
                    selectionCriteriaIndex + 1,
                    selectionCriteriaIndexMax,
                    currentDataSource
                );
            }

            // further selectionCriteria's we need the selectionCriteriaSource
            if (currentSelectionCriteria.getDto().selectionCriteriaSource) {
                let inheritDatesOfIndex =
                    currentSelectionCriteria.getDto().selectionCriteriaSource!.inheritDatesOfIndex;
                if (typeof inheritDatesOfIndex !== "undefined" && inheritDatesOfIndex != null) {
                    currentSelectionCriteria.setDatesOf(
                        this.chartDef.getSelectionCriteria(inheritDatesOfIndex).getDto()
                    );
                }

                // perform this analysis on the previous cumulativeDataSource
                let analyserTypeName =
                    currentSelectionCriteria.getDto().selectionCriteriaSource!.analyserType;
                // NB the analyser only needs to get its analyserConfig, currently
                const stage: Partial<ReportStageDto> = {
                    analyserConfig: {analyserType: analyserTypeName}
                };
                const analyserStub = new AnalyserReportStage(
                    this.chartDef,
                    stage as ReportStageDto,
                    -1
                );
                let analysedSequencePromise = cumulativeDataSource?.getData().then(data => {
                    return data.analyseWithForSequenceAnalysis(analyserStub);
                });
                // work out the new data source
                let intermediateCumulativeDataSource =
                    analysedSequencePromise == null
                        ? null
                        : new DataSourceWrapper(analysedSequencePromise);
                let newCumulativeDataSource = this.getSingleDataSource(
                    currentSelectionCriteria,
                    intermediateCumulativeDataSource
                );
                return this.joinDataSource(
                    selectionCriteriaIndex + 1,
                    selectionCriteriaIndexMax,
                    newCumulativeDataSource
                );
            }
            throw new Error(
                "missing selectionCriteriaSource on selectionMultiCriteria index: " +
                    selectionCriteriaIndex
            );
        } else {
            return cumulativeDataSource;
        }
    }

    private getSingleDataSource(
        currentSelectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any> | null
    ): DataSource<any> {
        return currentSelectionCriteria
            .getReportCapability()
            .getDataSource(this.chartDef, currentSelectionCriteria, previousDataSource);
    }
}

class DataSourceWrapper<T> implements DataSource<T> {
    constructor(private data: Promise<SequenceAnalysis<T>>) {}
    getData(): Promise<SequenceAnalysis<T>> {
        return this.data;
    }
}

abstract class TimeSeriesDataSource implements DataSource<CountsByMonthDto> {
    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        private previousDataSource: DataSource<CountsByMonthDto>
    ) {}

    abstract getDataInternal(): Promise<CountsByMonthDto[]>;

    getData(): Promise<SequenceAnalysis<CountsByMonthDto>> {
        const currentQ = this.getDataInternal().then(summaries => {
            summaries.forEach(s => (s.groupBy = this.currentSelectionCriteria.getDto().groupBy));
            return new ByMonthAnalysis(this.ctx, Lazy(summaries));
        });

        return currentQ.then(current => {
            return this.previousDataSource
                ? this.previousDataSource.getData().then(prev => {
                      return new ByMonthAnalysis(
                          this.ctx,
                          Lazy(prev.getData().concat(current.getData()))
                      );
                  })
                : current;
        });
    }
}

export class AnswersGroupedByQuestionAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsByQuestion(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

export class ReferralsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsByMonth(
            this.currentSelectionCriteria.getReportCriteriaDto(),
            this.currentSelectionCriteria.getDto().groupBy!
        );
    }
}

/**
 * Referrals 'live' would need a little work to be broken down per month (perhaps introducing liveStart and liveEnd dates)
 * so we just load the latest in the style of a CountsByMonthDto to aid a clean analyser.
 */
export class ReferralsGroupedByServiceAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsByService(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

/**
 * Referrals 'live' would need a little work to be broken down per month (perhaps introducing liveStart and liveEnd dates)
 * so we just load the latest in the style of a CountsByMonthDto to aid a clean analyser.
 */
export class ReferralsGroupedBySourceAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsBySource(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

export class ReferralsGroupedByEthnicityAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsByEthnicity(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

export class ReferralsGroupedBySexualOrientationAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsBySexualOrientation(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

export class ReferralsGroupedByDisabilityAsTimeSeriesDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findCountsByDisability(
            this.currentSelectionCriteria.getReportCriteriaDto()
        );
    }
}

export class TasksByMonthDataSource extends TimeSeriesDataSource {
    getDataInternal(): Promise<CountsByMonthDto[]> {
        return ReportAjaxRepository.instance.findTasksCountsByMonth(
            this.currentSelectionCriteria.getReportCriteriaDto(),
            this.currentSelectionCriteria.getDto().groupBy!
        );
    }
}

/** TODO: this might end up GroupedDataSource or something more generic */
export class GroupedWorkQueryDataSource implements DataSource<WorkAnalysisGroupSummary> {
    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {}

    getData(): Promise<SequenceAnalysis<WorkAnalysisGroupSummary>> {
        return ReportAjaxRepository.instance
            .findGroupedWorkAnalysis(
                this.currentSelectionCriteria.getReportCriteriaDto(),
                this.currentSelectionCriteria.getDto().groupBy!
            )
            .then(summaries => new GroupedWorkAnalysis(this.ctx, Lazy(summaries)));
    }
}

// NB Only BuildingWorkerRotaHandler implements 'rotaRepository.findServiceRecipientsByDate'
// and the handler expects one specific building, so it feels a like this was useful in the past
// see RotaAgreementQueryDataSource.
export class RotaDemandQueryDataSource implements DataSource<ServiceRecipientDemandDto> {
    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {}

    public getData(): Promise<SequenceAnalysis<ServiceRecipientDemandDto>> {
        return Promise.resolve().then(() => new RotaDemandAnalysis(this.ctx, Lazy([])));
        /*
        const criteria = this.currentSelectionCriteria.getReportCriteriaDto();
        const start = EccoDate.parseIso8601(criteria.from);
        const end = EccoDate.parseIso8601(criteria.to);
        return rotaRepository.findServiceRecipientsByDemandAndScheduleDate(start, end, "workers:all",
                this.currentSelectionCriteria.getDto().serviceRecipientFilter)
            .then( (recipients) => new RotaDemandAnalysis(this.ctx, Lazy(recipients)) );
        */
    }
}

export class RotaAgreementQueryDataSource implements DataSource<Agreement> {
    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {}

    public getData(): Promise<SequenceAnalysis<Agreement>> {
        return Promise.resolve().then(() => new RotaAgreementAnalysis(this.ctx, Lazy([])));
    }
    /*
    const criteria = this.currentSelectionCriteria.getReportCriteriaDto();
    const start = EccoDate.parseIso8601(criteria.from);
    const end = EccoDate.parseIso8601(criteria.to);
    return rotaRepository.findServiceRecipientsByDemandAndScheduleDate(start, end, "workers:all",
            this.currentSelectionCriteria.getDto().serviceRecipientFilter)
        .then( (recipients) => new RotaDemandAnalysis(this.ctx, Lazy(recipients)) );
    */
}

export class RotaScheduleQueryDataSource implements DataSource<DemandScheduleDto> {
    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {}

    public getData(): Promise<SequenceAnalysis<DemandScheduleDto>> {
        return Promise.resolve().then(() => new RotaScheduleAnalysis(this.ctx, Lazy([])));

        /*
        const criteria = this.currentSelectionCriteria.getReportCriteriaDto();
        const start = EccoDate.parseIso8601(criteria.from);
        const end = criteria.to ? EccoDate.parseIso8601(criteria.to) : EccoDate.todayUtc();

        const scheduleQ = rotaRepository.findAgreementsByScheduleDate(start, end)
            .then( (agreements) => {
                return Lazy(agreements)
                    .map(a => {
                        return a.getDemandSchedules().map(sch => {
                            sch.serviceRecipientId = a.getServiceRecipientId();
                            sch.serviceRecipientName = a.getServiceRecipientName();
                            return sch;
                        })
                        // we filter out the schedules from the agreements that we wanted from the request
                        .filter(schedule =>
                            start.earlierThanOrEqual(EccoDate.parseIso8601(schedule.end)) &&
                            schedule.end
                                ? end.laterThanOrEqual(EccoDate.parseIso8601(schedule.start))
                                : true
                        )
                    })
                    .flatten<DemandScheduleDto>()
                    .toArray();
            } );

        return scheduleQ
            .then(schedules => sequentialMapAll(schedules, s => this.getRelatedData(s)))
            .then((data) => new RotaScheduleAnalysis(this.ctx, Lazy(data)));
        */
    }

    private getRelatedData(schedule: DemandScheduleDto): Promise<DemandScheduleDto> {
        let schedQ = Promise.resolve(schedule);

        if (this.currentSelectionCriteria.fetchScheduleAptsConfirmedAtEnd()) {
            const end = EccoDate.parseIso8601(schedule.end);
            const start = end.subtractMonths(1);
            schedQ = schedQ.then(sched =>
                rotaRepository
                    .findScheduleRecurrencesConfirmed(sched.eventRef, start, end)
                    .then(events => {
                        sched.appointments = events;
                        return sched;
                    })
            );
        }

        return schedQ;
    }
}

/**
 * Similar to ReferralQueryDataSourceAbstract - load other data then the referral info
 */
class BaseRelatedQueryDataSource<
    T extends {
        serviceRecipientId: number;
        parentId?: number;
        referralSummary?: ReferralSummaryDto;
        serviceRecipient?: ServiceRecipient;
        client?: Client;
        staff?: StaffDto;
    }
> {

    protected getRelatedServiceRecipients(ctx: AnalysisContext, snapshots: T[]): Promise<T[]> {
        const dataBySrId = groupByNativeNumber(snapshots, s => s.serviceRecipientId);
        const chunkIds = Lazy(Object.keys(dataBySrId))
                .filter(isNumber)
                .map(s => parseInt(s))
                .chunk(10)
                .toArray();
        const singlePromise = (ids: number[]) =>
            // NB we could use loadSrWithEntitiesPromise to also load the underlying entities (possibly a lighter version needed)
            serviceRecipientRepository.findManyServiceRecipientByIds(ids).then(srs => {
                srs.forEach(sr => {
                    dataBySrId[sr.serviceRecipientId].forEach(s => {
                        s.serviceRecipient = sr;
                    });
                });
            });
        return sequentialMapAll(chunkIds, singlePromise).then(() => snapshots);
    }

    // TODO could use sequentialMapAll
    private chunkedRelatedReferralSummary(
        data: NumberToObjectMap<T[]>,
        uniqSrIds: string[],
        chunk: number
    ): Promise<void> {
        var ids = uniqSrIds.slice(chunk * 10, chunk * 10 + 10);
        if (ids.length == 0) {
            return Promise.resolve();
        }
        return referralRepository
            .findAllReferralSummaryByServiceRecipientId(ids)
            .then(refSums =>
                refSums.forEach(refSum => {
                    data[refSum.serviceRecipientId].forEach((s: T) => {
                        s.referralSummary = refSum;
                    });
                })
            )
            .then(() => this.chunkedRelatedReferralSummary(data, uniqSrIds, chunk + 1));
    }

    protected getRelatedReferralSummary(ctx: AnalysisContext, data: T[]): Promise<T[]> {
        const bySrId = groupByNativeNumber(data, data => data.serviceRecipientId);
        const uniqSrIds = Lazy(Object.keys(bySrId)).filter(isNumber).toArray();
        let allDataPopulatedQ = this.chunkedRelatedReferralSummary(bySrId, uniqSrIds, 0).then(
            () => {
                return data;
            }
        );

        // possibly if loading the referral we could load the client here
        //if (ctx.hasColumn("c: ")) {
        //}

        // r: worker
        if (ctx.hasColumn("r: worker")) {
            allDataPopulatedQ = allDataPopulatedQ.then(data => {
                const byContactId = groupByNativeNumber(
                    data,
                    value => value.referralSummary!.supportWorkerId!
                );
                const chunkedSupportWorkerIds = Lazy(Object.keys(byContactId))
                    .filter(isNumber)
                    .map(s => parseInt(s))
                    .chunk(10)
                    .toArray();
                const singlePromise = (ids: number[]) =>
                    contactRepository.findAllContactsByContactId(ids).then(contacts =>
                        contacts.forEach(contact => {
                            byContactId[contact.contactId].forEach(d => {
                                if (isIndividual(contact)) {
                                    d.referralSummary!.supportWorkerDisplayName =
                                        contact.firstName + " " + contact.lastName;
                                } else {
                                    d.referralSummary!.supportWorkerDisplayName =
                                        "id: " + contact.contactId.toString();
                                }
                            });
                        })
                    );

                return sequentialMapAll(chunkedSupportWorkerIds, singlePromise).then(() => data);
            });
        }

        return allDataPopulatedQ;
    }

    protected getRelatedClients(data: T[]): Promise<T[]> {
        return ReferralQueryDataSourceAbstract.getRelatedSummaryClients(data);
    }

    protected getRelatedStaff(ctx: AnalysisContext, snapshots: T[]): Promise<T[]> {
        const dataBySrId = groupByNativeNumber(snapshots, s => s.serviceRecipientId);
        const chunkIds = Lazy(Object.keys(dataBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            workersRepository.findWorkersByServiceRecipientIds(ids).then(workers => {
                return workers.forEach(w => {
                    w.jobs.forEach(j => {
                        dataBySrId[j.serviceRecipient.serviceRecipientId].forEach(s => {
                            s.staff = w;
                        });
                    });
                });
            });

        return sequentialMapAll(chunkIds, singlePromise).then(() => snapshots);
    }
}

/**
 * Load other data first (obeying the criteria), then add the referral
 * NB Used by ReferralQueryDataSource and RelatedWorkThenReferralQueryDataSource (evidence reports)
 */
class ReferralQueryDataSourceAbstract {
    protected currentReportCriteria: ReportCriteriaDto;

    constructor(
        protected ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        protected previousDataSource: DataSource<any>,
        protected hactSession: boolean
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    protected referralsFromPreviousDataSource(): Promise<ReferralDto[]> {
        let repoMethod: ((id: number) => Promise<ReferralDto[]>) | null = null;
        switch (this.currentSelectionCriteria.getDto().selectionCriteriaSource?.entityIdType) {
            case "serviceRecipientId":
                repoMethod = id =>
                    ReferralQueryDataSourceAbstract.wrapPromiseToPromiseArray(
                        referralRepository.findOneReferralSummaryByServiceRecipientIdAsReferral(id)
                    );
                break;
            case "id":
                repoMethod = id =>
                    ReferralQueryDataSourceAbstract.wrapPromiseToPromiseArray(
                        referralRepository.findOneReferralSummary(id)
                    );
                break;
            case "clientId":
                repoMethod = id =>
                    referralRepository.findAllReferralWithoutSecuritySummaryByClientAsReferral(id);
                break;
            default:
                throw new Error(
                    "this dataSource does not support the entityIdType: " +
                        this.currentSelectionCriteria.getDto().selectionCriteriaSource?.entityIdType
                );
        }

        const referrals: ReferralDto[] = [];
        const idsQ = this.previousDataSource.getData().then(seqAnalysis => {
            return seqAnalysis
                .getData()
                .toArray()
                .map(s => parseInt(s));
        });
        return idsQ.then(ids => {
            return sequentialMapAll(ids, id => repoMethod!(id).then(r => referrals.concat(r))).then(
                () => referrals
            );
        });
    }

    private static wrapPromiseToPromiseArray<SR extends ServiceRecipient>(
        single: Promise<SR>
    ): Promise<SR[]> {
        return single.then(referral => {
            return [referral];
        });
    }

    /**
     * Recursive call to get referrals until nothing is brought back.
     * Paging works better than splitting by date ranges, since paging works with non-dated reports, so is a consistent
     * approach. Pagination doesn't necessarily solve timeouts though since some queries need to be paged in memory
     * (eg left join fetch) and so loads the data anyway. To solve that, we can make the loads simpler entities and join
     * them as we do here in ReportDataSourceFactory.
     *
     * @param page zero-based page number
     * @param cumulativeResult
     */
    protected pagedReferrals(
        page: number,
        cumulativeResult: ReferralDto[]
    ): Promise<ReferralDto[]> {
        return ReportAjaxRepository.instance
            .findAllReferrals(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReferrals(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    protected pagedReferralSummary(
        page: number,
        cumulativeResult: ReferralDto[]
    ): Promise<ReferralDto[]> {
        // debug report with a specific c-id
        //return referralRepository.findAllReferralWithoutSecuritySummaryByClientAsReferral(61);

        return ReportAjaxRepository.instance
            .findAllReferralSummary(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReferralSummary(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    public static getRelatedSummaryClients<T extends {referralSummary?: ReferralSummaryDto, client?: Client}>(ras: T[]): Promise<T[]> {
        const rasByClient = groupByNativeNumber(ras, ra => ra.referralSummary?.clientId);
        const chunkedIds = Lazy(Object.keys(rasByClient))
                .filter(isNumber)
                .map(s => parseInt(s))
                .chunk(50)
                .toArray();
        const singlePromise = (ids: number[]) => clientRepository.findAllClientsByClientId(ids)
                .then(clients =>
                        clients.forEach(client => {
                                    rasByClient[client.clientId!].forEach(ra => {
                                        ra.client = client;
                                    })
                                }
                        ));
        return sequentialMapAll(chunkedIds, singlePromise)
                .then(() => ras);
    }

    public getRelatedClients<T extends {referral: ReferralDto, client?: Client}>(ras: T[]): Promise<T[]> {
        const rasByClient = groupByNativeNumber(ras, ra => ra.referral.clientId);
        const chunkedIds = Lazy(Object.keys(rasByClient))
                .filter(isNumber)
                .map(s => parseInt(s))
                .chunk(50)
                .toArray();
        const singlePromise = (ids: number[]) => clientRepository.findAllClientsByClientId(ids)
                .then(clients =>
                        clients.forEach(client => {
                                    rasByClient[client.clientId!].forEach(ra => {
                                        ra.client = client;
                                    })
                                }
                        ));
        return sequentialMapAll(chunkedIds, singlePromise)
                .then(() => ras);
    }

    protected hactSessionDataQuery(): Promise<HactSessionData> {
        return hactRepository.findHactSessionData();
    }

    protected referralSummaryWithEntities(referralQ: Promise<ReferralDto>) {

        // supportWorkerId into supportWorkerName
        referralQ = referralQ.then(result => {
            if (result.supportWorkerId == null) {
                return result;
            } else {
                return contactRepository.findOneContact(result.supportWorkerId).then(contact => {
                    if (isIndividual(contact)) {
                        result.supportWorkerDisplayName =
                            contact.firstName + " " + contact.lastName;
                    } else {
                        result.supportWorkerDisplayName = "id: " + contact.contactId.toString();
                    }
                    return result;
                });
            }
        });

        // interviewer1Id into interviewer1Name
        referralQ = referralQ.then(result => {
            if (result.interviewer1ContactId == null) {
                return result;
            } else {
                return contactRepository
                    .findOneContact(result.interviewer1ContactId)
                    .then(contact => {
                        if (isIndividual(contact)) {
                            result.interviewer1WorkerDisplayName =
                                contact.firstName + " " + contact.lastName;
                        } else {
                            result.interviewer1WorkerDisplayName =
                                "id: " + contact.contactId.toString();
                        }
                        return result;
                    });
            }
        });

        // exitReasonId into exitReason
        referralQ = referralQ.then(result => {
            if (result.exitReasonId != null) {
                result.exitReason = this.ctx
                    .getSessionData()
                    .getListDefinitionEntryById(result.exitReasonId)
                    ?.getDisplayName();
            }
            return result;
        });

        // signpostedReasonId into signpostedReason
        referralQ = referralQ.then(result => {
            if (result.signpostedReasonId == null) {
                return result;
            } else {
                result.signpostedReason = this.ctx
                    .getSessionData()
                    .getListDefinitionEntryById(result.signpostedReasonId)
                    ?.getDisplayName();
                return result;
            }
        });

        // signpostedAgencyId into signpostedAgencyName
        referralQ = referralQ.then(result => {
            if (result.signpostedAgencyId == null) {
                return result;
            } else {
                return companyRepository.findOneCompany(result.signpostedAgencyId).then(contact => {
                    result.signpostedAgencyName = contact.companyName;
                    return result;
                });
            }
        });

        // source of referral
        referralQ = referralQ.then(result => {
            if (result.selfReferral) {
                result.source = "self referral"; // TODO create message for ReferralToViewModel.sourceAsSelfReferral
                return result;
            } else if (!result.referrerAgencyId && result.referrerIndividualId) {
                result.source = "individual"; // TODO create message - see ReferralToViewModel.sourceOfReferral
                return result;
            } else if (result.referrerAgencyId) {
                let agencyQ = companyRepository
                    .findOneAgency(result.referrerAgencyId)
                    .then(contact => {
                        result.source = contact.companyName;
                        result.sourceAgency = contact;
                        return result;
                    });
                if (result.referrerIndividualId) {
                    agencyQ = agencyQ.then(result => {
                        return contactRepository
                            .findOneIndividual(result.referrerIndividualId)
                            .then(contact => {
                                result.sourceProfessional = contact || undefined;
                                return result;
                            });
                    });
                }
                return agencyQ;
            } else {
                return result; // source of referral hasn't been done yet
            }
        });

        return referralQ;
    }
}

/**
 * Load referral first, then add other data - EXCEPT this 'other data' LOADS THE WORLD
 * TODO start to restrict this fetchEntities, but it can be used when we know the data is minimal (eg exit questionnaire)
 *
 * fetchEntities doesn't get the criteria applied because what would we apply? If we want to restrict
 * the entities to the report criteria, then switch the selectionRootEntity / fetchRelatedEntities around,
 * and load the work first.
 *
 * NB ReferralFull uses the full referral, but Referral and ReferralSummary use the summary.
 * NB They all use this, ReferralQueryDataSource.
 *
 * TODO push more entity loading into ReferralQueryDataSourceAbstract, so that it can be shared
 *  between ReferralQueryDataSource at line ~700 and RelatedWorkThenReferralQueryDataSource ~1100 (.addServiceRecipients)
 * NB we do currently extend from ReferralQueryDataSourceAbstract which shares pagedReferral / pageReferralSummary
 */

export class ReferralQueryDataSource
    extends ReferralQueryDataSourceAbstract
    implements DataSource<ReferralAggregate>
{
    constructor(
        ctx: AnalysisContext,
        currentSelectionCriteria: SelectionCriteria,
        previousDataSource: DataSource<any>,
        hactSession?: boolean,
        private fetchSummary = false,
        private fetchSummaryEntities = true
    ) {
        super(ctx, currentSelectionCriteria, previousDataSource, hactSession || false);
    }

    public getData(): Promise<SequenceAnalysis<ReferralAggregate>> {
        let allReferralsQ: Promise<ReferralDto[]> = this.previousDataSource
            ? this.referralsFromPreviousDataSource()
            : this.fetchSummary
            ? this.pagedReferralSummary(0, [])
            : this.pagedReferrals(0, []);

        // Adds some basics we perhaps expected since we changed to referral summary by default (5518a875).
        // Reuses referralSummaryWithEntities also used by ReferralRelatedWorkQueryDataSource which loads the referral
        // data after other data (although that does use findOneReferralSummaryByServiceRecipientIdAsReferral).
        if (this.fetchSummary && this.fetchSummaryEntities) {
            allReferralsQ = allReferralsQ.then(r => this.getRelatedEntities(r));
        }

        let rasQ: Promise<ReferralAggregate[]> = allReferralsQ.then(referrals =>
            referrals.map(referral => ({
                referral: referral,
                reportCriteria: this.currentReportCriteria
            }))
        );

        // TODO push more entity loading into ReferralQueryDataSourceAbstract, so that it can be shared
        //  between ReferralQueryDataSource at line ~700 and RelatedWorkThenReferralQueryDataSource ~1100 (.addServiceRecipients)
        //  eg ReferralQueryDataSource has chunked getRelatedContacts, getRelatedDataGrouped (chunked clients, events)
        // NB we do currently extend from ReferralQueryDataSourceAbstract which shares pagedReferral / pageReferralSummary

        // always load the referrer's
        rasQ = rasQ.then(ras => this.getRelatedContacts(ras));

        // if fetch...(), then load additional data in bulk
        rasQ = this.getRelatedDataGrouped(rasQ);

        // if fetch...(), then load additional data
        return rasQ
            .then(ras =>
                // TODO push more entity loading into ReferralQueryDataSourceAbstract, so that it can be shared
                sequentialMapAll(ras, ra => this.getRelatedData(ra))
            )
            .then(referrals => new ReferralAggregateAnalysis(this.ctx, Lazy(referrals)));
    }

    /**
     * Adds some basics we perhaps expected since we changed to referral summary by default (5518a875).
     * This could be done server-side, but we want to keep the server id-only and we simply cache client-side.
     * The requests could also be batched client-side as per getRelatedContacts.
     */
    private getRelatedEntities(referrals: ReferralDto[]): Promise<ReferralDto[]> {
        return sequentialMapAll(referrals, r =>
            this.referralSummaryWithEntities(Promise.resolve(r))
        );
    }

    private getRelatedEvents(
        ras: ReferralAggregate[],
        start: EccoDate,
        end: EccoDate
    ): Promise<ReferralAggregate[]> {
        const rasByContact = groupByNativeNumber(ras, ra => ra.referral.contactId);
        const rasBySrId = groupByNativeNumber(ras, ra => ra.referral.serviceRecipientId);
        const populateChunk = (idsIn: number[], startIn: EccoDate, endIn: EccoDate) =>
            calendarRepository
                .fetchCalendarsByContactIds(idsIn, startIn, endIn)
                .then(contactEvents => {
                    const eventsBySrId = groupByNativeNumber(
                        contactEvents,
                        e => e.serviceRecipientId!
                    );
                    Object.entries(eventsBySrId).forEach(([srId, eventsForSrId]) => {
                        // check the events from contactId match the srId's in our index
                        if (rasBySrId[Number(srId)]) {
                            rasBySrId[Number(srId)].forEach(
                                ra => (ra.referral.calendarEvents = eventsForSrId)
                            );
                        }
                    });
                });
        const chunkedIds = Lazy(Object.keys(rasByContact))
            .filter(isNumber)
            .map(id => parseInt(id))
            .chunk(20)
            .toArray();
        const singlePromise = (ids: number[]) => populateChunk(ids, start, end);
        return sequentialMapAll(chunkedIds, singlePromise).then(() => Promise.resolve(ras));
    }

    private getRelatedContacts(ras: ReferralAggregate[]): Promise<ReferralAggregate[]> {
        const rasByContactForProfessional = groupByNativeNumber(
            ras,
            ra => ra.referral.referrerAgencyId && ra.referral.referrerIndividualId
        );
        const chunkedIds = Lazy(Object.keys(rasByContactForProfessional))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(50)
            .toArray();
        const singlePromise = (ids: number[]) =>
            contactRepository.findAllIndividualsByIndividualId(ids).then(contacts =>
                contacts.forEach(contact => {
                    rasByContactForProfessional[contact.contactId].forEach(ra => {
                        ra.referral.sourceProfessional = contact;
                    });
                })
            );
        return sequentialMapAll(chunkedIds, singlePromise).then(() => ras);
    }

    /**
     * Deprecated for non-hact - need to move to snapshot approach for goal data.
     * This loads all supportWork for a number of srIds which is really poor.
     * We have AnalysisContext to get the dates - eg this.ctx.getReportFrom()
     * However, the 'goals' reports do require the whole history.
     *
     * For hact, loading the whole history across srId is appropriate as this
     * only loads where WantToAchieve, statusChange and where actionDefId is a hact mapping.
     */
    // TODO performance we should stop this - its not paged or dated
    //  but does at least only load status changes (potentially limited to hact only smart steps also)
    //  see SupportEvidenceController.findAllByServiceRecipientIds
    //  NB this should really be for one-off data, like exit qnr etc, which support isn't
    private getRelatedSupportWorkByStatusChange(
        ras: ReferralAggregate[],
        hactOnly: boolean
    ): Promise<ReferralAggregate[]> {
        const rasBySrId = groupByNativeNumber(ras, ra => ra.referral.serviceRecipientId);
        const idsChunked = Lazy(Object.keys(rasBySrId))
            .filter(isNumber)
            .map(id => parseInt(id))
            .chunk(1) // A conservative figure for now as support work can be quite big
            .toArray();
        const singlePromise = (ids: number[]) =>
            supportWorkRepository
                .findAllSupportWorkByStatusChangesByServiceRecipientIds(ids, undefined, hactOnly)
                .then(works =>
                    works.forEach(work => {
                        if (work.length > 0) {
                            rasBySrId[work[0].serviceRecipientId].forEach(ra => {
                                ra.supportWork = workCommonAnalysis.retainChangedActions(
                                    Lazy(work)
                                );
                            });
                        }
                    })
                );
        const promises = sequentialMapAll(idsChunked, singlePromise);
        /*
                .then((worksBySrId: SupportWork[][]) => {
                    worksBySrId.forEach(worksOfSrId => {
                        if (worksOfSrId.length > 0) {
                            rasBySrId[worksOfSrId[0].serviceRecipientId].forEach(ra => {
                                ra.supportWork = workCommonAnalysis.retainChangedActions(Lazy(worksOfSrId));
                            });
                            if (withEvents) {
                                let swsByEventId = _.groupBy(worksOfSrId, work => work.eventId);
                                calendarRepository.fetchEventsById(Object.keys(swsByEventId)).then(events => {
                                    Object.keys(swsByEventId).forEach(key => {
                                        swsByEventId[key].forEach(work => work.event = events.filter(ev => ev.id == key)[0]);
                                    });
                                });
                            }
                        }
                    });
                })*/

        return promises.then(() => {
            // Put an empty sequence in for each where no work was returned.  This allows mis-configured analysers
            // to still be detected by throwing an error if supportWork is null.
            ras.forEach(ra => {
                ra.supportWork = ra.supportWork || Lazy([]);
            });
            return ras;
        });
    }

    private getRelatedDataGrouped(
        rasQ: Promise<ReferralAggregate[]>
    ): Promise<ReferralAggregate[]> {
        // GROUPED queries, other 'fetch' in
        if (this.currentSelectionCriteria.fetchClientDetail()) {
            rasQ = rasQ.then(ras => this.getRelatedClients(ras));
        }

        // TODO calls "performance we should stop this - its not paged or dated"
        //  this is when Referral is the criteria, and other things are loaded
        if (this.currentSelectionCriteria.fetchSupportWork()) {
            let withHact = this.currentSelectionCriteria.fetchHactSessionData();
            rasQ = rasQ.then(ras => this.getRelatedSupportWorkByStatusChange(ras, withHact));
        }

        // NB LOAD TODAY'S EVENTS ONLY
        // To get many days, we could do an event-first report such as EventQueryDataSource extends BaseRelatedQueryDataSource<EventResourceDto>.
        // However, loading events require contactIds which comes from the referrals - so we need to load referrals first.
        // This then begs the question - should we be allowing the loading of referrals based on status/date range of the referrals?
        // We end up wanting two date ranges in the criteria, which is a similar problem to SupportWorkQueryDataSource (which also uses BaseRelatedQueryDataSource),
        // which comes from SupportWorkReportCapability which uses 'withReferralStatus' - does it use the same date range??
        // The solution should require a date range for both entities (referrals / events) independently.
        // Without that, there are choices for us now:
        //  1 - load events first (via a fixed set of referrals) (eg live) then load events by the range
        //  2 - load referrals first (this report) and re-use the dates
        // However, option 2 is tricky since the referral report obeys the dates, and we need from/to.
        // So, we need option 1 with a fixed live referral load
        if (this.currentSelectionCriteria.fetchReferralEvents()) {
            // NB same as parsing currentSelectionCriteria.getReportCriteriaDto();
            // 'liveAtEnd' is the reportTo date (with a blank reportFrom date)
            const end = this.ctx.getReportTo() || EccoDate.todayUtc();
            // start is the same as end - the server runs to the end of the 'end' day
            rasQ = rasQ.then(ras => this.getRelatedEvents(ras, end, end));
        }

        return rasQ;
    }

    /** Get additional data related to this referral where necessary.
     */
    private getRelatedData(ra: ReferralAggregate): Promise<ReferralAggregate> {
        const referral = ra.referral;

        let raQ = Promise.resolve(ra);

        if (this.hactSession) {
            raQ = raQ.then(ra =>
                this.hactSessionDataQuery().then(sessionData => {
                    ra.hactSessionData = sessionData;
                    return ra;
                })
            );
        }

        // this fits related data even though its the same result for each referral - its a cached query
        raQ = raQ.then(result => {
            result.sessionData = this.ctx.getSessionData();
            return result;
        });

        // TODO performance we should stop this - its not paged or dated
        if (this.currentSelectionCriteria.fetchRiskWork()) {
            raQ = raQ.then(result =>
                riskWorkRepository
                    .findRiskWorkByServiceRecipientId(referral.serviceRecipientId)
                    .then(work => {
                        result.riskWork = workCommonAnalysis.riskRetainChangedActions(Lazy(work));
                        return result;
                    })
            );
        }
        // TODO performance we should stop this - its not paged or dated
        //  but this probably isn't used anymore
        if (this.currentSelectionCriteria.fetchActivityInterest()) {
            raQ = raQ.then(result =>
                activityTypeRepository
                    .findActivityInterestsByServiceRecipientId(
                        referral.parentServiceRecipientId || referral.serviceRecipientId
                    )
                    .then(activityInterest => {
                        result.activityInterest = Lazy(activityInterest);
                        return result;
                    })
            );
        }
        // group support restricted to from/to
        if (this.currentSelectionCriteria.fetchActivityInvolvement()) {
            raQ = raQ.then(result =>
                groupSupportRepository
                    .findActivityInvolvementByReferralId(
                        referral.referralId,
                        this.getFromDate()!,
                        this.getToDate()!
                    )
                    .then(groupActivities => {
                        if (
                            this.currentSelectionCriteria.getDto().selectionPropertyPath ==
                            "groupActivityDate"
                        ) {
                            groupActivities = groupActivities.filter(entry =>
                                this.isActiveWithinDateRange(entry)
                            );
                        }
                        result.groupActivities = Lazy(groupActivities);
                        return result;
                    })
            );
        }
        // TODO performance we should stop this - its not paged or dated
        if (this.currentSelectionCriteria.fetchSingleValueHistory()) {
            raQ = raQ.then(result =>
                singleValueHistoryRepository
                    .findAllByServiceRecipientOrderByKeyAscValidFromDesc(
                        referral.serviceRecipientId
                    )
                    .then(svHistory => {
                        // if there is a fromDate (not an 'as at' report) then find all in the range
                        const from = this.getFromDate();
                        const to = this.getToDate()!;
                        if (from) {
                            const svAround = svHistory.filter(entry =>
                                ReferralQueryDataSource.isSVHAroundDateRange(entry, from, to)
                            );
                            result.singleValueHistory = Lazy(svAround);
                            return result;
                            // if there is no fromDate (an 'as at' report) then find the first in the range
                        } else {
                            result.singleValueHistory = Lazy(svHistory)
                                .filter(entry =>
                                    ReferralQueryDataSource.isSVHUnderToDate(entry, to)
                                )
                                .groupBy(svh => svh.key) // so far this will be the 'value' with an array of svh
                                .pairs() // pairs moves this structure into an array of ['value', {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}[]]
                                .map(extractPair) // turns the pairs array into a Group {key: array[0], elements: array[1]}
                                .map(groupTuple => groupTuple.elements.first()) // choose the first under the to date
                                .flatten<SingleValueHistoryDto>();
                            return result;
                        }
                    })
            );
        }

        // NB moved since the SupportWork report is its own report now - since svh was last used
        //if (this.currentSelectionCriteria.fetchSingleValueQuestionnaireHistory()) {
        //}

        // TODO performance we should stop this - its not paged or dated
        //  BUT this could be for one-off data, like exit qnr etc, as it loads all history
        // SEE if fetchQuestionnaireSnapshot is more appropriate
        if (this.currentSelectionCriteria.fetchQuestionnaireWork()) {
            const evidenceGroupArr =
                this.currentSelectionCriteria.getDto().questionnaireEvidenceGroup;
            if (Array.isArray(evidenceGroupArr)) {
                throw new Error("change the defn - don't get all data for all questionnaires");
            } else {
                const evidenceGroupStr = EvidenceGroup.fromName(evidenceGroupArr!);
                const requestCurrentReportCriteria = JSON.parse(
                    JSON.stringify(this.currentReportCriteria)
                );
                requestCurrentReportCriteria.questionnaireEvidenceGroup = evidenceGroupStr;
                raQ = raQ.then(result =>
                    questionnaireWorkRepository
                        .findQuestionnaireWorkByServiceRecipientId(
                            referral.serviceRecipientId,
                            evidenceGroupStr
                        )
                        .then(work => {
                            result.questionnaireWork = Lazy(work);
                            return result;
                        })
                );
            }
        }

        if (this.currentSelectionCriteria.fetchQuestionnaireSnapshotAtEnd()) {
            // using the end SnapshotPeriod.DURING2 means still applying the start date of the period,
            // so by indicating 'fetch...End' we are implying we want to ignore the start
            // which we can do by fudging the criteria when loading the snapshots
            const reportOverride = {...this.currentReportCriteria, from: null};
            const snapshotsQ = QuestionnaireBaseSnapshotQueryDataSource.getData(
                this.ctx,
                this.currentSelectionCriteria,
                reportOverride,
                [SnapshotPeriod.DURING2]
            );
            raQ = raQ.then(result =>
                snapshotsQ.then(snapshots => {
                    result.questionnaireWork = Lazy(
                        snapshots.map(s => {
                            const work: Partial<QuestionnaireWorkDto> = {
                                answers: s.answers
                            };
                            return work as QuestionnaireWorkDto;
                        })
                    );
                    return result;
                })
            );
        }

        // gets the latest custom form work
        // NB form work tends to always mean the latest snapshot - but possibly should be the latest at that point in time
        if (this.currentSelectionCriteria.fetchCustomFormWork()) {
            const evidenceGroupStr = this.currentSelectionCriteria.getDto().customFormEvidenceGroup;
            const evidenceGroup = EvidenceGroup.fromName(evidenceGroupStr!);
            raQ = raQ.then(result =>
                getFormEvidenceRepository()
                    .findLatestFormEvidenceSnapshotByServiceRecipientId(
                        referral.serviceRecipientId,
                        evidenceGroup
                    )
                    .then(work => {
                        result.customFormWorkLatest = work || undefined;
                        return result;
                    })
            );
        }

        if (this.currentSelectionCriteria.fetchServiceRecipientCommandAtEnd()) {
            raQ = raQ.then(result => {
                const adjustForScope: ReportCriteriaDto = {
                    ...this.currentReportCriteria,
                    serviceRecipientFilter: ":" + result.referral.serviceRecipientId
                };
                return serviceRecipientRepository
                    .findServiceRecipientCommandsBySearch(adjustForScope, 0)
                    .then(cmd => {
                        result.serviceRecipientCommandLatest = cmd.length > 0 ? cmd[0] : undefined;
                        return result;
                    });
            });
        }

        // TODO share / group this
        if (this.currentSelectionCriteria.fetchRelatedReferrals()) {
            raQ = raQ.then(result =>
                referralRepository
                    .findRelatedReferralsToPrimary(referral.referralId)
                    .then(relationships => {
                        result.relatedReferrals = Lazy(relationships);
                        return result;
                    })
            );
        }

        // TODO share / group this
        if (this.currentSelectionCriteria.fetchAssociatedContacts()) {
            raQ = raQ.then(result =>
                referralRepository
                    .findAssociatedContactsByServiceRecipientId(referral.serviceRecipientId)
                    .then(data => {
                        result.contacts = Lazy(data);
                        return result;
                    })
            );
        }

        // NB this is referral-based, but we may need to be client-based
        if (this.currentSelectionCriteria.fetchFinanceCharges()) {
            raQ = raQ.then(result =>
                // call per client to get all charges (not paged - shouldn't need it)
                financeRepository.findChargesBySrId(referral.serviceRecipientId)
                    .then(data => {
                        financeRepository.findReceiptsBySrId(referral.serviceRecipientId)
                            .findAllFinanceReceipts(this.currentReportCriteria, page)
                            .then(result => {
                        result.charges = Lazy(data);
                        return result;
                    })
            );
        }

        return raQ;
    }

    private getFromDate() {
        return EccoDate.parseIso8601(this.currentReportCriteria.from);
    }

    private getToDate() {
        return EccoDate.parseIso8601(this.currentReportCriteria.to);
    }

    /** Does this involvement overlap with the date range of interest */
    private isActiveWithinDateRange(involvement: groupSupportDtos.ClientAttendanceDto): boolean {
        const activityStart = EccoDateTime.parseIso8601Utc(
            involvement.parentActivity!.startDateTime
        ).toEccoDate();
        const fmDte = this.getFromDate()!;
        const toDte = this.getToDate()!;
        if (!involvement.parentActivity!.endDate) {
            return activityStart.laterThanOrEqual(fmDte) && activityStart.earlierThan(toDte);
        } else {
            const activityEnd = EccoDate.parseIso8601(involvement.parentActivity!.endDate);
            return activityEnd.laterThanOrEqual(fmDte) && activityStart.earlierThan(toDte);
        }
    }

    /** Does this involvement overlap with the date range of interest */
    public static isSVHAroundDateRange(
        svh: SingleValueHistoryDto,
        from: EccoDate,
        to: EccoDate
    ): boolean {
        const start = EccoDateTime.parseIso8601Utc(svh.validFrom).toEccoDate();
        if (!svh.validTo) {
            // no validTo means its the latest which is always valid from validFrom
            return start.earlierThan(to);
        } else {
            const end = EccoDateTime.parseIso8601(svh.validTo).toEccoDate();
            return end.laterThanOrEqual(from) && start.earlierThan(to);
        }
    }

    /** Does this involvement fit under the date range of interest */
    public static isSVHUnderToDate(svh: SingleValueHistoryDto, to: EccoDate): boolean {
        const start = EccoDateTime.parseIso8601Utc(svh.validFrom).toEccoDate();
        return start.earlierThan(to);
    }
}

/**
 * Load other data first (obeying the criteria), then add the referral
 */
export class RelatedWorkThenReferralQueryDataSource
    extends ReferralQueryDataSourceAbstract
    implements DataSource<ReferralAggregate> {
    private supportByServiceRecipientId: types.SparseArray<SupportWork[]> = {};
    private questionnaireByServiceRecipientId: types.SparseArray<QuestionnaireWorkDto[]> = {};
    private customFormByServiceRecipientId: types.SparseArray<FormEvidence<any>[]> = {};

    constructor(
            ctx: AnalysisContext,
            currentSelectionCriteria: SelectionCriteria,
            previousDataSource: DataSource<any>,
            private supportWork: boolean,
            private questionnaireWork: boolean,
            private customForm: boolean,
            hactSession: boolean
    ) {
        super(ctx, currentSelectionCriteria, previousDataSource, hactSession);
    }

    public getData(): Promise<SequenceAnalysis<ReferralAggregate>> {
        // get supportWork IN RANGE - because it calls /reports/evidence/needs/page/{page}/
        // which calls applyLocalDateRange in evidencePredicate
        const supportQ = this.supportWork ? this.supportWorkQuery() : Promise.resolve({});

        // get supportWork IN RANGE - because it calls /reports/evidence/needs/page/{page}/
        // which calls applyLocalDateRange in evidencePredicate
        const questionnaireQ = this.questionnaireWork
                ? this.questionnaireWorkQuery()
                : Promise.resolve({});

        // UNIMPLEMENTED
        const customFormQ = this.customForm ? this.customFormWorkQuery() : Promise.resolve({});

        // only 3 promises instigated at once, so okay to use .all?
        return Promise.all([supportQ, questionnaireQ, customFormQ]).then(
                ([support, questionnaire, customForm]) => {
                    const supportSrids = Object.keys(support);
                    const questionnaireSrids = Object.keys(questionnaire);
                    const customFormSrids = Object.keys(customForm);
                    const srids = Lazy(supportSrids)
                            .union(questionnaireSrids)
                            .union(customFormSrids)
                            .toArray();

                    return this.addServiceRecipients(srids).then(
                            referrals => new ReferralAggregateAnalysis(this.ctx, Lazy(referrals))
                    );
                }
        );
    }

    private supportWorkQuery(): Promise<types.SparseArray<evidenceDto.SupportWork[]>> {
        let workQ = this.pagedSupportWork(0, []);

        if (this.currentSelectionCriteria.fetchSupportWorkEvents()) {
            workQ = workQ.then(works => this.getRelatedEvents(works));
        }

        return workQ.then(works => {
            works.forEach(work => this.addToSupportArray(work));
            return this.supportByServiceRecipientId;
        });
    }

    private getRelatedEvents(works: evidenceDto.SupportWork[]): Promise<evidenceDto.SupportWork[]> {
        const wByEventId = groupByNativeString(works, work => work.eventId);
        const chunkedIds = Lazy(Object.keys(wByEventId))
                // TODO replace with keyBy
                .filter(id => id != "null")
                .chunk(10)
                .toArray();
        const singlePromise = (ids: string[]) =>
                calendarRepository.fetchEventsById(ids).then(events =>
                        events.forEach(e => {
                            wByEventId[e.uid].forEach(work => {
                                work.event = e;
                            });
                        })
                );
        return sequentialMapAll(chunkedIds, singlePromise).then(() => works);
    }

    private questionnaireWorkQuery(): Promise<types.SparseArray<QuestionnaireWorkDto[]>> {
        const evidenceGroupStr = this.currentSelectionCriteria.getDto().questionnaireEvidenceGroup;
        if (Array.isArray(evidenceGroupStr)) {
            return evidenceGroupStr
                    .reduce(
                            (chainedP: Promise<QuestionnaireWorkDto[]>, eg: string) =>
                                    chainedP.then(allWork =>
                                            this.pagedQuestionnaireWork(0, eg, []).then(work =>
                                                    allWork.concat(work)
                                            )
                                    ),
                            Promise.resolve<QuestionnaireWorkDto[]>([])
                    )
                    .then(w => {
                        w.forEach(work => this.addToQuestionnaireArray(work));
                        return this.questionnaireByServiceRecipientId;
                    });
        } else {
            return this.pagedQuestionnaireWork(0, evidenceGroupStr!, []).then(work => {
                work.forEach(work => this.addToQuestionnaireArray(work));
                return this.questionnaireByServiceRecipientId;
            });
        }
    }

    private customFormWorkQuery(): Promise<types.SparseArray<FormEvidence<any>[]>> {
        return RelatedWorkThenReferralQueryDataSource.pagedCustomFormWork(0, []).then(work => {
            work.forEach(work => this.addToCustomFormArray(work));
            return this.customFormByServiceRecipientId;
        });
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedSupportWork(
            page: number,
            cumulativeResult: evidenceDto.SupportWork[]
    ): Promise<evidenceDto.SupportWork[]> {
        return ReportAjaxRepository.instance
                .findAllSupportWork(this.currentReportCriteria, page)
                .then(result => {
                    if (result.length) {
                        return this.pagedSupportWork(page + 1, cumulativeResult.concat(result));
                    } else {
                        return Promise.resolve(cumulativeResult);
                    }
                });
    }

    private addToSupportArray(work: SupportWork) {
        if (!this.supportByServiceRecipientId[work.serviceRecipientId]) {
            this.supportByServiceRecipientId[work.serviceRecipientId] = new Array<SupportWork>();
        }
        this.supportByServiceRecipientId[work.serviceRecipientId].push(work);
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param page zero-based page number
     * @param evidenceGroupStr the evidence to get the data from
     * @param cumulativeResult
     */
    private pagedQuestionnaireWork(
            page: number,
            evidenceGroupStr: string,
            cumulativeResult: QuestionnaireWorkDto[]
    ): Promise<QuestionnaireWorkDto[]> {
        const evidenceGroup = EvidenceGroup.fromName(evidenceGroupStr);
        const requestCurrentReportCriteria = JSON.parse(JSON.stringify(this.currentReportCriteria));
        requestCurrentReportCriteria.questionnaireEvidenceGroup = evidenceGroupStr;
        return ReportAjaxRepository.instance
                .findAllQuestionnaireWork(requestCurrentReportCriteria, evidenceGroup, page)
                .then(result => {
                    if (result.length) {
                        return this.pagedQuestionnaireWork(
                                page + 1,
                                evidenceGroupStr,
                                cumulativeResult.concat(result)
                        );
                    } else {
                        return Promise.resolve(cumulativeResult);
                    }
                });
    }

    private addToQuestionnaireArray(work: QuestionnaireWorkDto) {
        if (!this.questionnaireByServiceRecipientId[work.serviceRecipientId]) {
            this.questionnaireByServiceRecipientId[work.serviceRecipientId] =
                    new Array<QuestionnaireWorkDto>();
        }
        this.questionnaireByServiceRecipientId[work.serviceRecipientId].push(work);
    }

    /**
     * Recursive call to get custom work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private static pagedCustomFormWork(
            page: number,
            cumulativeResult: FormEvidence<any>[]
    ): Promise<FormEvidence<any>[]> {
        // TODO UNIMPLEMENTED - we don't currently need to load custom form data as an entity itself
        throw new Error("not implemented");
        /*
        const evidenceGroupStr = this.currentSelectionCriteria.getDto().customFormEvidenceGroup;
        return ReportAjaxRepository.instance.findAllFormEvidenceWork(this.currentReportCriteria, evidenceGroupStr, page)
            .then((result) => {
                if (result.length) {
                    return this.pagedCustomFormWork(page+1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
        */
    }

    private addToCustomFormArray(work: FormEvidence<any>) {
        if (!this.customFormByServiceRecipientId[work.serviceRecipientId]) {
            this.customFormByServiceRecipientId[work.serviceRecipientId] = new Array<
                    FormEvidence<any>
            >();
        }
        this.customFormByServiceRecipientId[work.serviceRecipientId].push(work);
    }

    protected getSupportWork(serviceRecipientId: number) {
        return this.supportByServiceRecipientId[serviceRecipientId];
    }

    protected getQuestionnaireWork(serviceRecipientId: number) {
        return this.questionnaireByServiceRecipientId[serviceRecipientId];
    }

    protected getCustomFormWork(serviceRecipientId: number) {
        return this.customFormByServiceRecipientId[serviceRecipientId];
    }


    // TODO push more entity loading into ReferralQueryDataSourceAbstract, so that it can be shared
    //  between ReferralQueryDataSource at line ~700 and this addServiceRecipients
    //  eg ReferralQueryDataSource has chunked getRelatedContacts, getRelatedDataGrouped (chunked clients, events)
    // NB we do currently extend from ReferralQueryDataSourceAbstract which shares pagedReferral / pageReferralSummary
    protected addServiceRecipients(srids: string[]): Promise<ReferralAggregate[]> {

        // just a counter
        let ctr = 0;

        const singlePromise = (serviceRecipientIdStr: string) => {
            const serviceRecipientId = parseInt(serviceRecipientIdStr);

            // NB RelatedWorkThenReferral reports get the referral summary, unless referralFull is specifically requested
            // BUT referralSummaryWithEntities is assumed if NOT referralFull
            let referralQ: Promise<ReferralDto> = this.currentSelectionCriteria.fetchReferralFull()
                    ? referralRepository.findOneReferralByServiceRecipientId(serviceRecipientId)
                    : referralRepository.findOneReferralSummaryByServiceRecipientIdAsReferral(
                            serviceRecipientId
                    );
            if (!this.currentSelectionCriteria.fetchReferralFull()) {
                // loads from the common base ReferralQueryDataSourceAbstract
                referralQ = this.referralSummaryWithEntities(referralQ);
            }

            let singleValueHistory: Sequence<SingleValueHistoryDto>;
            if (this.currentSelectionCriteria.fetchSingleValueHistory()) {
                referralQ = referralQ.then(result =>
                        singleValueHistoryRepository
                                .findAllByServiceRecipientOrderByKeyAscValidFromDesc(
                                        result.serviceRecipientId
                                )
                                .then(svHistory => {
                                    singleValueHistory = Lazy(svHistory);
                                    return result;
                                })
                );
            }

            // client detail
            let client: Client | null = null;
            if (this.currentSelectionCriteria.fetchClientDetail()) {
                referralQ = referralQ.then(result =>
                        clientRepository.findOneClientCached(result.clientId).then(c => {
                            client = c;
                            return result;
                        })
                );
            }

            // attach the data we've already loaded
            let rasQ = referralQ.then(referral => {
                const supportWork = this.getSupportWork(serviceRecipientId);
                const workWithStatusChanged = supportWork
                        ? workCommonAnalysis.retainChangedActions(Lazy(supportWork))
                        : null;
                const questionnaireWork = this.getQuestionnaireWork(serviceRecipientId)
                        ? Lazy(this.getQuestionnaireWork(serviceRecipientId))
                        : null;
                const customFormWork = this.getCustomFormWork(serviceRecipientId)
                        ? Lazy(this.getCustomFormWork(serviceRecipientId))
                        : null;
                return {
                    referral: referral,
                    client: client || undefined,
                    supportWork: workWithStatusChanged || undefined,
                    questionnaireWork: questionnaireWork || undefined,
                    customFormWork: customFormWork || undefined,
                    singleValueHistory: singleValueHistory,
                    sessionData: this.ctx.getSessionData(),
                    reportCriteria: this.currentReportCriteria
                };
            });

            if (this.currentSelectionCriteria.fetchSingleValueQuestionnaireHistory()) {
                rasQ = rasQ
                        // findAllByServiceRecipientOrderByKeyAscValidFromDesc
                        .then((result) => {
                            const supportHrsGrp = this.ctx.getSessionData().getServiceTypeByServiceCategorisationId(result.referral.serviceAllocationId).getTaskDefinitionSetting("referralView", "supportHoursGrp");
                            // paged, and sorted by WorkDateDescCreatedDesc
                            /*const criteriaQ = ReportAjaxRepository.generateLiveReportCriteria()
                                    .then(criteria => {
                                        //criteria.to = EccoDateTime.parseIso8601(this.activity.startDateTime).toEccoDate().formatIso8601();
                                        /!*this.pagedReferralSummary(criteria as ReportCriteriaDto, 0, [])
                                                .then(referrals => {
                                                    referrals.forEach(r => this.addReferral(r));
                                                });*!/
                                        return criteria;
                                    });*/
                            const criteria: SelectionCriteriaDto = {
                                fetchRelatedEntities: [],
                                questionnaireEvidenceGroup: supportHrsGrp,
                                selectionRootEntity: "Questionnaire",
                                serviceRecipientFilter: `serviceRecipient:${result.referral.serviceRecipientId}`,
                                userId: this.ctx.getSessionData().getDto().userId,
                                username: this.ctx.getSessionData().getDto().username
                            }
                            return QuestionnaireQueryDataSource.questionnaireWorkQuery(new SelectionCriteria(criteria))
                                    .then((qnrHistory) => {
                                        // grab the evidenceGroup and the questionId of the support hours 'choices'
                                        const supportHrsQnStr =
                                            this.ctx
                                                .getSessionData()
                                                .getServiceTypeByServiceCategorisationId(
                                                    result.referral.serviceAllocationId
                                                )
                                                .getTaskDefinitionSetting(
                                                    "referralView",
                                                    "supportHoursQn"
                                                ) || "0";
                                        const supportHrsQn = parseInt(supportHrsQnStr);
                                        // filter the support hours question
                                        const relevantAnswers = qnrHistory
                                            .map(qnr => qnr.answers)
                                            .reduce((r, x) => r.concat(x), []) // flatMap
                                            .filter(ans => ans.questionId == supportHrsQn);
                                        // map to a svh
                                        const svHistoryRaw = relevantAnswers.map(ans => {
                                            // NB assume the answer is the choice 'value'
                                            // eg low/med/high value 10, 15, 20
                                            // although we could just assume the value is the answer
                                            const valueStr = ans.answer;
                                            const value = valueStr ? parseInt(valueStr) : null;
                                            // NB 'id' is used in the calcs a lot - but just to ensure we are grouping the work by the entry
                                            // the svh doesn't understand hours, just an entity to group by, so the resulting report column is where 'hours' are shown
                                            const svh: SingleValueHistoryDto = {
                                                id: ctr, // used as a surrogate id, would be qnr.id which is a uuid
                                                serviceRecipientId:
                                                    result.referral.serviceRecipientId,
                                                key: "hours", // the entity we are working on - always support hours, probably should be supportHoursQn but this displays
                                                value: value, // direct value or ref
                                                valueRef: supportHrsQn,
                                                validFrom: ans.workDate!,
                                                validTo: null
                                            };
                                            ctr = ctr + 1;
                                            return svh;
                                        });

                                        // make sure we add a first item, so that we capture all work before the first svh
                                        const firstSvh: SingleValueHistoryDto = {
                                            id: -1, // qnr.id is a uuid TODO check id not really used
                                            serviceRecipientId: result.referral.serviceRecipientId,
                                            key: "hours", // the entity we are working on
                                            value: null, // direct value or ref
                                            validFrom: "1970-01-01T00:00:00.000", //EccoDate.parseIso8601FromDateTime("1970-01-01T00:00"),
                                            validTo: null
                                        };
                                        svHistoryRaw.unshift(firstSvh);

                                        // fill in the validTo based on the previous validFrom TODO check previous day / same day
                                        // ordered by newest first, so go from the second element using the first
                                        const updatePeriods = (
                                            periods: SingleValueHistoryDto[]
                                        ): SingleValueHistoryDto[] => {
                                            if (periods.length <= 1) {
                                                return periods;
                                            }
                                            // if there are 2 elements, update one
                                            for (let i = periods.length - 2; i >= 0; i--) {
                                                periods[i].validTo = periods[i + 1].validFrom;
                                            }
                                            return periods;
                                        };
                                        const svHistoryByDate = svHistoryRaw.sort((a, b) =>
                                            a.validFrom.localeCompare(b.validFrom)
                                        );
                                        const svHistory = updatePeriods(svHistoryByDate);

                                        // as per original SVH
                                        // if there is a fromDate (not an 'as at' report) then find all in the range
                                        const from = EccoDate.parseIso8601(
                                            this.currentReportCriteria.from
                                        );
                                        const to = EccoDate.parseIso8601(
                                            this.currentReportCriteria.to
                                        )!;
                                        // ?? should we use 'from' or 'getReportFrom' - see 79b46fd1
                                        if (this.ctx.getReportFrom()) {
                                            const svAround = svHistory.filter(entry =>
                                                ReferralQueryDataSource.isSVHAroundDateRange(
                                                    entry,
                                                    from!,
                                                    to
                                                )
                                            );
                                            result.singleValueHistory = Lazy(svAround);
                                            return result;
                                            // if there is no fromDate (an 'as at' report) then find the first in the range
                                        } else {
                                            result.singleValueHistory = Lazy(svHistory)
                                                .filter(entry =>
                                                    ReferralQueryDataSource.isSVHUnderToDate(
                                                        entry,
                                                        to
                                                    )
                                                )
                                                .groupBy(svh => svh.key) // so far this will be the 'value' with an array of svh
                                                .pairs() // pairs moves this structure into an array of ['value', {svh: SingleValueHistoryDto; work: WorkWithRefToReferralAggregate}[]]
                                                .map(extractPair) // turns the pairs array into a Group {key: array[0], elements: array[1]}
                                                .map(groupTuple => groupTuple.elements.first()) // choose the first under the to date
                                                .flatten<SingleValueHistoryDto>();
                                            return result;
                                        }
                                    })
                        });
            }

            return rasQ;
        };

        return sequentialMapAll(srids, singlePromise);
    }
}


/**
 * An independent class (ie not part of ReferralAggregate) to assist with our need
 * to load the HACT answers for a period. This is then used to load all the relevant referrals
 * and reload all the questionnaire history. Instead of making a very specific class
 * (like ReferralAggregate) we opt to make a more generic solution which can be re-used
 * and potentially provides more powerful reporting where each data source can have its own
 * reportCriteria and analysis to join together (akin to what was available in the demoware).
 * In fact this approach could be used to break up the current ReferralAggregate if wanted.
 *
 * Incidentally we could just ask for the srIds here since that is all we currently use it for
 * but this would involve more infrastructure, and the http cache should re-use this request anyway.
 */
export class QuestionnaireQueryDataSource
    extends BaseRelatedQueryDataSource<QuestionnaireWorkDto>
    implements DataSource<QuestionnaireWorkDto>
{
    //private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        //this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    // see RelatedWorkThenReferralQueryDataSource for other examples of loading questionnaire data first
    // or what should be BaseRelatedQueryDataSource
    public getData(): Promise<SequenceAnalysis<QuestionnaireWorkDto>> {
        // load questionnaire work, obeying criteria reports/evidence/questionnaire/
        // note that we don't load referral data here, but QuestionnaireReportCapability
        // calls instead RelatedWorkThenReferralQueryDataSource if a referral is requested
        // (where the questionnaire query still obeys the report criteria)
        let qnQ = QuestionnaireQueryDataSource.questionnaireWorkQuery(this.currentSelectionCriteria);

        if (this.currentSelectionCriteria.fetchServiceRecipient()) {
            qnQ = qnQ.then(s => this.getRelatedServiceRecipients(this.ctx, s));
        }

        if (this.currentSelectionCriteria.fetchStaff()) {
            qnQ = qnQ.then(s => this.getRelatedStaff(this.ctx, s));
        }

        return qnQ.then(qnWork => new QnAnswerAnalysis(this.ctx, Lazy(qnWork)));
    }

    public static questionnaireWorkQuery(currentSelectionCriteria: SelectionCriteria): Promise<QuestionnaireWorkDto[]> {
        const evidenceGroupStr = currentSelectionCriteria.getDto().questionnaireEvidenceGroup!;
        if (Array.isArray(evidenceGroupStr)) {
            return evidenceGroupStr.reduce(
                (chainedP: Promise<QuestionnaireWorkDto[]>, eg: string) =>
                    chainedP.then(allWork =>
                        QuestionnaireQueryDataSource.pagedQuestionnaireWork(
                            currentSelectionCriteria,
                            0,
                            eg,
                            []
                        ).then(work => allWork.concat(work))
                    ),
                Promise.resolve([])
            );
        } else {
            return QuestionnaireQueryDataSource.pagedQuestionnaireWork(
                currentSelectionCriteria,
                0,
                evidenceGroupStr,
                []
            ).then(work => {
                return work;
            });
        }
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param currentSelectionCriteria
     * @param page zero-based page number
     * @param evidenceGroupStr the evidence to get the data from
     * @param cumulativeResult
     */
    private static pagedQuestionnaireWork(
        currentSelectionCriteria: SelectionCriteria,
        page: number,
        evidenceGroupStr: string,
        cumulativeResult: QuestionnaireWorkDto[]
    ): Promise<QuestionnaireWorkDto[]> {
        const currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
        const evidenceGroup = EvidenceGroup.fromName(evidenceGroupStr);
        const requestCurrentReportCriteria = JSON.parse(JSON.stringify(currentReportCriteria));
        requestCurrentReportCriteria.questionnaireEvidenceGroup = evidenceGroupStr;
        return ReportAjaxRepository.instance
            .findAllQuestionnaireWork(requestCurrentReportCriteria, evidenceGroup, page)
            .then(result => {
                if (result.length) {
                    return QuestionnaireQueryDataSource.pagedQuestionnaireWork(
                        requestCurrentReportCriteria,
                        page + 1,
                        evidenceGroupStr,
                        cumulativeResult.concat(result)
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 */
export class QuestionnaireBaseSnapshotQueryDataSource
    implements DataSource<QuestionnaireAnswersSnapshotDto>
{
    private readonly currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        protected snapshots: SnapshotPeriod[]
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<QuestionnaireAnswersSnapshotDto>> {
        return QuestionnaireBaseSnapshotQueryDataSource.getData(
            this.ctx,
            this.currentSelectionCriteria,
            this.currentReportCriteria,
            this.snapshots
        ).then(data => new QuestionnaireSnapshotOnlyAnalysis(this.ctx, Lazy(data)));
    }

    public static getData(
        ctx: AnalysisContext,
        currentSelectionCriteria: SelectionCriteria,
        currentReportCriteria: ReportCriteriaDto,
        snapshots: SnapshotPeriod[]
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        let latestBeforeQ =
            snapshots.indexOf(SnapshotPeriod.PRE) > -1
                ? QuestionnaireBaseSnapshotQueryDataSource.getDataSnapshot(
                      ctx,
                      currentSelectionCriteria,
                      currentReportCriteria,
                      SnapshotPeriod.PRE
                  )
                : Promise.resolve([] as QuestionnaireAnswersSnapshotDto[]);
        let earliestWithinQ =
            snapshots.indexOf(SnapshotPeriod.DURING1) > -1
                ? QuestionnaireBaseSnapshotQueryDataSource.getDataSnapshot(
                      ctx,
                      currentSelectionCriteria,
                      currentReportCriteria,
                      SnapshotPeriod.DURING1
                  )
                : Promise.resolve([] as QuestionnaireAnswersSnapshotDto[]);
        let latestWithinQ =
            snapshots.indexOf(SnapshotPeriod.DURING2) > -1
                ? QuestionnaireBaseSnapshotQueryDataSource.getDataSnapshot(
                      ctx,
                      currentSelectionCriteria,
                      currentReportCriteria,
                      SnapshotPeriod.DURING2
                  )
                : Promise.resolve([] as QuestionnaireAnswersSnapshotDto[]);

        // NB order is important as it is assumed in the Analysis
        return latestBeforeQ.then(latestBefore => {
            return earliestWithinQ.then(earliestWithin => {
                return latestWithinQ.then(latestWithin => {
                    return latestBefore.concat(earliestWithin.concat(latestWithin));
                });
            });
        });
    }

    private static getDataSnapshot(
        ctx: AnalysisContext,
        currentSelectionCriteria: SelectionCriteria,
        currentReportCriteria: ReportCriteriaDto,
        snapshotPeriod: SnapshotPeriod
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        return this.questionnaireSnapshotQuery(snapshotPeriod, currentReportCriteria).then(
            snapshot => {
                return sequentialMapAll(snapshot, s => {
                    // TODO: batch tasks up and make getRelatedData take an array
                    s.snapshotPeriod = snapshotPeriod;
                    return this.getRelatedData(ctx, currentSelectionCriteria, s);
                });
            }
        );
    }

    private static questionnaireSnapshotQuery(
        snapshotPeriod: SnapshotPeriod,
        currentReportCriteria: ReportCriteriaDto
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        return this.pagedQuestionnaireSnapshot(snapshotPeriod, 0, [], currentReportCriteria).then(
            snapshot => {
                return snapshot;
            }
        );
    }

    /**
     * Recursive call to get activities until nothing is brought back.
     * @param snapshotPeriod
     * @param page zero-based page number
     * @param cumulativeResult
     * @param currentReportCriteria
     */
    private static pagedQuestionnaireSnapshot(
        snapshotPeriod: SnapshotPeriod,
        page: number,
        cumulativeResult: QuestionnaireAnswersSnapshotDto[],
        currentReportCriteria: ReportCriteriaDto
    ): Promise<QuestionnaireAnswersSnapshotDto[]> {
        let promiseQ: Promise<QuestionnaireAnswersSnapshotDto[]> = Promise.resolve([]);
        switch (snapshotPeriod) {
            case SnapshotPeriod.PRE:
                promiseQ =
                    ReportAjaxRepository.instance.findAllQuestionnaireLatestSnapshotsBeforeRange(
                        currentReportCriteria,
                        page
                    );
                break;
            case SnapshotPeriod.DURING1:
                promiseQ =
                    ReportAjaxRepository.instance.findAllQuestionnaireEarliestSnapshotsInRange(
                        currentReportCriteria,
                        page
                    );
                break;
            case SnapshotPeriod.DURING2:
                promiseQ = ReportAjaxRepository.instance.findAllQuestionnaireLatestSnapshotsInRange(
                    currentReportCriteria,
                    page
                );
                break;
        }

        return promiseQ.then(result => {
            return result.length
                ? this.pagedQuestionnaireSnapshot(
                      snapshotPeriod,
                      page + 1,
                      cumulativeResult.concat(result),
                      currentReportCriteria
                  )
                : Promise.resolve(cumulativeResult);
        });
    }

    /** Automatically load additional data related to this activity */
    private static getRelatedData(
        ctx: AnalysisContext,
        currentSelectionCriteria: SelectionCriteria,
        snapshot: QuestionnaireAnswersSnapshotDto
    ): Promise<QuestionnaireAnswersSnapshotDto> {

        // the default is to get the referral, but we can override with fetchServiceRecipient
        if (currentSelectionCriteria.fetchServiceRecipient()) {
            const srQ = serviceRecipientRepository.findOneServiceRecipientById(snapshot.serviceRecipientId);
            return srQ.then(sr => {
                snapshot.serviceRecipient = sr;
                return snapshot;
            });

        } else {
            const referralQ = referralRepository.findOneReferralSummaryByServiceRecipientIdUsingDto(snapshot.serviceRecipientId);

            let relatedQ = referralQ.then(referral => {
                // the more useful aspects to display
                snapshot.referralSummary = referral;
                snapshot.sessionData = ctx.getSessionData();
                return snapshot;
            });

            if (currentSelectionCriteria.fetchClientDetail()) {
                relatedQ = relatedQ.then(related => clientRepository.findOneClientCached(snapshot.referralSummary.clientId)
                        .then(c => {
                            related.client = c || undefined;
                            return related;
                        })
                );
            }
            return relatedQ;
        }
    }
}

export class QuestionnaireLatestSnapshotQueryDataSource extends QuestionnaireBaseSnapshotQueryDataSource {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [SnapshotPeriod.DURING2]);
    }
}

export class QuestionnaireMultiSnapshotQueryDataSource extends QuestionnaireBaseSnapshotQueryDataSource {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [
            SnapshotPeriod.PRE,
            SnapshotPeriod.DURING1,
            SnapshotPeriod.DURING2
        ]);
    }
}

export class SmartStepBaseSnapshotQueryDataSource
    extends BaseRelatedQueryDataSource<BaseActionInstanceSnapshotDto>
    implements DataSource<BaseActionInstanceSnapshotDto>
{
    private readonly currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        protected snapshots: SnapshotPeriod[]
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<BaseActionInstanceSnapshotDto>> {
        let latestBeforeQ =
            this.snapshots.indexOf(SnapshotPeriod.PRE) > -1
                ? this.getDataSnapshot(SnapshotPeriod.PRE)
                : Promise.resolve([]);
        // NB we don't use DURING1 for "selectionRootEntity": "SmartStepMultiSnapshot"
        let earliestWithinQ =
            this.snapshots.indexOf(SnapshotPeriod.DURING1) > -1
                ? this.getDataSnapshot(SnapshotPeriod.DURING1)
                : Promise.resolve([]);
        let latestWithinQ =
            this.snapshots.indexOf(SnapshotPeriod.DURING2) > -1
                ? this.getDataSnapshot(SnapshotPeriod.DURING2)
                : Promise.resolve([]);

        let snapshotsQ = latestBeforeQ.then(latestBefore => {
            return earliestWithinQ.then(earliestWithin => {
                return latestWithinQ.then(latestWithin => {
                    return latestBefore.concat(earliestWithin.concat(latestWithin));
                });
            });
        });

        if (this.currentSelectionCriteria.fetchReferral()) {
            snapshotsQ = snapshotsQ.then(s => this.getRelatedReferralSummary(this.ctx, s));
        }

        if (this.currentSelectionCriteria.fetchStaff()) {
            snapshotsQ = snapshotsQ.then(s => this.getRelatedStaff(this.ctx, s));
        }

        return snapshotsQ.then(all => {
            return new SmartStepsSnapshotOnlyAnalysis(this.ctx, Lazy(all));
        });
    }

    private getDataSnapshot(
        snapshotPeriod: SnapshotPeriod
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        return this.smartStepsSnapshotQuery(snapshotPeriod).then(snapshot => {
            // return a promise
            return Promise.resolve(
                snapshot.map(snapshot => {
                    snapshot.snapshotPeriod = snapshotPeriod;
                    return snapshot;
                })
            );
        });
    }

    private smartStepsSnapshotQuery(
        snapshotPeriod: SnapshotPeriod
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        return this.pagedSmartStepsSnapshot(snapshotPeriod, 0, []).then(snapshot => {
            return snapshot;
        });
    }

    /**
     * Recursive call to get activities until nothing is brought back.
     * @param snapshotPeriod
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedSmartStepsSnapshot(
        snapshotPeriod: SnapshotPeriod,
        page: number,
        cumulativeResult: BaseActionInstanceSnapshotDto[]
    ): Promise<BaseActionInstanceSnapshotDto[]> {
        let promiseQ: Promise<BaseActionInstanceSnapshotDto[]> = Promise.resolve([]);
        switch (snapshotPeriod) {
            case SnapshotPeriod.PRE:
                // defer to repository methods to indicate whether to load support or risk
                promiseQ =
                    ReportAjaxRepository.instance.findAllSmartStepsLatestSnapshotsBeforeRange(
                        this.currentReportCriteria,
                        page
                    );
                break;
            case SnapshotPeriod.DURING1:
                promiseQ = ReportAjaxRepository.instance.findAllSmartStepsEarliestSnapshotsInRange(
                    this.currentReportCriteria,
                    page
                );
                break;
            case SnapshotPeriod.DURING2:
                promiseQ = ReportAjaxRepository.instance.findAllSmartStepsLatestSnapshotsInRange(
                    this.currentReportCriteria,
                    page
                );
                break;
        }

        return promiseQ.then(result => {
            return result.length
                ? this.pagedSmartStepsSnapshot(
                      snapshotPeriod,
                      page + 1,
                      cumulativeResult.concat(result)
                  )
                : Promise.resolve(cumulativeResult);
        });
    }
}

export class SmartStepLatestSnapshotQueryDataSource extends SmartStepBaseSnapshotQueryDataSource {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [SnapshotPeriod.DURING2]);
    }
}

export class SmartStepMultiSnapshotQueryDataSource extends SmartStepBaseSnapshotQueryDataSource {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [
            SnapshotPeriod.PRE,
            SnapshotPeriod.DURING1,
            SnapshotPeriod.DURING2
        ]);
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 * group support
 */
export class ActivityAttendanceQueryDataSource implements DataSource<ClientAttendance> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<ClientAttendance>> {
        let q = this.activityAttendanceQuery(this.ctx.getSessionData());
        if (this.ctx.hasColumnStart("r: ")) {
            q = q.then(attendances => this.getRelatedReferralSummary(attendances));
        }
        return q.then(
            attendances => new ActivityAttendanceOnlyAnalysis(this.ctx, Lazy(attendances))
        );
    }

    private activityAttendanceQuery(sessionData: SessionData): Promise<ClientAttendance[]> {
        let q = this.pagedActivityAttendance(0, [], sessionData);

        // gets the latest custom form work
        // NB form work tends to always mean the latest snapshot - but possibly should be the latest at that point in time
        if (this.currentSelectionCriteria.fetchCustomFormWork()) {
            q = q.then(gs => {
                const dtos = gs.map(g => g.getDto());
                return this.getRelatedCustomForms(dtos).then(() => gs); // return the original gs data
            });
        }

        return q;
    }

    /**
     * Recursive call to get activities until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     * @param sessionData
     */
    private pagedActivityAttendance(
        page: number,
        cumulativeResult: ClientAttendance[],
        sessionData: SessionData
    ): Promise<ClientAttendance[]> {
        return ReportAjaxRepository.instance
            .findActivityAttendancesByStartDate(this.currentReportCriteria, page)
            .then(results => {
                let result = results.map(dto => new ClientAttendance(dto, sessionData));
                if (result.length) {
                    return this.pagedActivityAttendance(
                        page + 1,
                        cumulativeResult.concat(result),
                        sessionData
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getRelatedReferralSummary(
        attendances: ClientAttendance[]
    ): Promise<ClientAttendance[]> {
        const bySrId = groupByNativeNumber(
            attendances,
            attendance => attendance.getDto().serviceRecipientId
        );
        const chunkedIds = Lazy(Object.keys(bySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            referralRepository.findAllReferralSummaryByServiceRecipientId(ids).then(refSums =>
                refSums.forEach(refSum => {
                    bySrId[refSum.serviceRecipientId].forEach(entity => {
                        entity.getDto().referralSummary = refSum;
                    });
                })
            );

        return sequentialMapAll(chunkedIds, singlePromise).then(() => attendances);
    }

    private getRelatedCustomForms<
        T extends {
            serviceRecipientId: number;
            customFormWorkLatest?: FormEvidence<any> | null | undefined;
        }
    >(data: T[]): Promise<T[]> {
        const evidenceGroupStr = this.currentSelectionCriteria.getDto().customFormEvidenceGroup!;
        const evidenceGroup = EvidenceGroup.fromName(evidenceGroupStr);

        const bySrId = groupByNativeNumber(data, d => d.serviceRecipientId);
        const singlePromise = (srId: number) =>
            getFormEvidenceRepository()
                .findLatestFormEvidenceSnapshotByServiceRecipientId(srId, evidenceGroup)
                .then(work => {
                    bySrId[srId].forEach(d => {
                        d.customFormWorkLatest = work;
                    });
                });
        const ids = data.map(d => d.serviceRecipientId);
        return sequentialMapAll(ids, singlePromise).then(() => data);
    }
}

/**
 * Using the smartstep snapshot report but adapting for only the snapshot work so far, which is 'latestWithin'.
 */
export abstract class WorkBaseSnapshotQueryDataSource<T extends {serviceRecipientId: number}>
    extends BaseRelatedQueryDataSource<T>
    implements DataSource<T>
{
    private readonly currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        protected snapshots: SnapshotPeriod[]
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<T>> {
        let snapshotsQ: Promise<T[]> =
            this.snapshots.indexOf(SnapshotPeriod.DURING2) > -1
                ? this.getDataSnapshot(SnapshotPeriod.DURING2)
                : Promise.resolve([]);

        if (this.currentSelectionCriteria.fetchReferral()) {
            snapshotsQ = snapshotsQ.then(s => this.getRelatedReferralSummary(this.ctx, s));
        }

        if (this.currentSelectionCriteria.fetchStaff()) {
            snapshotsQ = snapshotsQ.then(s => this.getRelatedStaff(this.ctx, s));
        }

        return snapshotsQ.then(all => {
            return this.getAnalysis(this.ctx, all);
        });
    }

    protected abstract getAnalysis(ctx: AnalysisContext, data: T[]): SequenceAnalysis<T>;

    private getDataSnapshot(snapshotPeriod: SnapshotPeriod): Promise<T[]> {
        return this.workSnapshotQuery(snapshotPeriod);
        /*
            .then((snapshot) => {

                return Promise.all(snapshot
                    .map((snapshot) => {
                        //snapshot.snapshotPeriod = snapshotPeriod;
                        return snapshot;
                    }));
            });
        */
    }

    private workSnapshotQuery(snapshotPeriod: SnapshotPeriod): Promise<T[]> {
        return this.pagedWorkSnapshot(snapshotPeriod, 0, []).then(snapshot => {
            return snapshot;
        });
    }

    /**
     * Recursive call to get work until nothing is brought back.
     * @param snapshotPeriod
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedWorkSnapshot(
        snapshotPeriod: SnapshotPeriod,
        page: number,
        cumulativeResult: T[]
    ): Promise<T[]> {
        let promiseQ: Promise<T[]> = Promise.resolve([]);
        switch (snapshotPeriod) {
            case SnapshotPeriod.PRE:
                throw new Error("undefined SnapshotPeriod.DURING1");
            case SnapshotPeriod.DURING1:
                throw new Error("undefined SnapshotPeriod.DURING1");
            case SnapshotPeriod.DURING2:
                promiseQ = this.findWorkLatestSnapshotsInRange(this.currentReportCriteria, page);
                break;
        }

        return promiseQ.then(result => {
            return result.length
                ? this.pagedWorkSnapshot(snapshotPeriod, page + 1, cumulativeResult.concat(result))
                : Promise.resolve(cumulativeResult);
        });
    }

    protected abstract findWorkLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<T[]>;
}

export class SupportWorkLatestSnapshotQueryDataSource extends WorkBaseSnapshotQueryDataSource<SupportWork> {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [SnapshotPeriod.DURING2]);
    }

    protected getAnalysis(
        ctx: AnalysisContext,
        data: SupportWork[]
    ): SequenceAnalysis<SupportWork> {
        return new SupportWorkOnlyAnalysis(ctx, Lazy(data));
    }

    protected findWorkLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<SupportWork[]> {
        return ReportAjaxRepository.instance.findSupportWorkLatestSnapshotsInRange(reportDto, page);
    }
}

export class RiskWorkLatestSnapshotQueryDataSource extends WorkBaseSnapshotQueryDataSource<RiskWork> {
    constructor(ctx: AnalysisContext, currentSelectionCriteria: SelectionCriteria) {
        super(ctx, currentSelectionCriteria, [SnapshotPeriod.DURING2]);
    }

    protected getAnalysis(ctx: AnalysisContext, data: RiskWork[]): SequenceAnalysis<RiskWork> {
        return new RiskWorkOnlyAnalysis(ctx, Lazy(data));
    }

    protected findWorkLatestSnapshotsInRange(
        reportDto: ReportCriteriaDto,
        page: number
    ): Promise<RiskWork[]> {
        return ReportAjaxRepository.instance.findRiskWorkLatestSnapshotsInRange(reportDto, page);
    }
}

/**
 * An independent class
 */
export class ServiceRecipientCommandQueryDataSource
    implements DataSource<BaseServiceRecipientCommandDto>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<BaseServiceRecipientCommandDto>> {
        return this.serviceRecipientCommandQuery().then(commands => {
            return new ServiceRecipientCommandOnlyAnalysis(this.ctx, Lazy(commands));
        });
    }

    private serviceRecipientCommandQuery(): Promise<BaseServiceRecipientCommandDto[]> {
        const srCommands = this.pagedServiceRecipientCommand(0, []);
        const srDeletedCommands = this.pagedServiceRecipientDeletedCommand(0, []);

        // re-sort different source data into one ordered list
        // only 2 promises instigated at once, so okay to use .all?
        return Promise.all([srCommands, srDeletedCommands]).then(
            ([srCommands, srDeletedCommands]) => {
                // data is ordered by created ASC, as does this sort (with both data sets)
                // NB CommandHistoryListControl uses reverse()
                return srCommands
                    .concat(srDeletedCommands)
                    .sort((a, b) =>
                        EccoDateTime.parseIso8601Utc(a.timestamp).compare(
                            EccoDateTime.parseIso8601Utc(b.timestamp)
                        )
                    );
            }
        );
    }

    /**
     * Recursive call to get activities until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedServiceRecipientCommand(
        page: number,
        cumulativeResult: BaseServiceRecipientCommandDto[]
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return ReportAjaxRepository.instance
            .findServiceRecipientCommandsByCreated(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedServiceRecipientCommand(
                        page + 1,
                        cumulativeResult.concat(result)
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
    /**
     * Recursive call to get archived activities until nothing is brought back.
     * Specifically this is for deleteReqSvcRec and deleteSvcRec
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedServiceRecipientDeletedCommand(
        page: number,
        cumulativeResult: BaseServiceRecipientCommandDto[]
    ): Promise<BaseServiceRecipientCommandDto[]> {
        return ReportAjaxRepository.instance
            .findServiceRecipientDeletedCommandsByCreated(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedServiceRecipientDeletedCommand(
                        page + 1,
                        cumulativeResult.concat(result)
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 */
export class CustomFormLatestQueryDataSource implements DataSource<FormEvidence<any>> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        protected ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<FormEvidence<any>>> {
        return this.customFormWorkQuery().then(customFormWorks => {
            return new CustomFormWorkOnlyAnalysis(this.ctx, Lazy(customFormWorks));
        });
    }

    private customFormWorkQuery(): Promise<FormEvidence<any>[]> {
        return this.pagedCustomFormWork(0, []).then(work => {
            return work;
        });
    }

    /**
     * Recursive call to get custom work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedCustomFormWork(
        page: number,
        cumulativeResult: FormEvidence<any>[]
    ): Promise<FormEvidence<any>[]> {
        const evidenceGroup = EvidenceGroup.fromName(
            this.currentSelectionCriteria.getDto().customFormEvidenceGroup!
        );
        return ReportAjaxRepository.instance
            .findAllFormEvidenceLatestSnapshot(this.currentReportCriteria, evidenceGroup, page)
            .then(result => {
                if (result.length) {
                    return this.pagedCustomFormWork(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 */
export class SupportWorkQueryDataSource
    extends BaseRelatedQueryDataSource<SupportWork>
    implements DataSource<SupportWork>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        protected ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<SupportWork>> {
        let supportWorkQ = this.supportWorkQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            supportWorkQ = supportWorkQ.then(supportWorks =>
                this.getRelatedReferralSummary(this.ctx, supportWorks)
            );
        }

        return supportWorkQ.then(
            supportWorks => new SupportWorkOnlyAnalysis(this.ctx, Lazy(supportWorks))
        );
    }

    private supportWorkQuery(): Promise<evidenceDto.SupportWork[]> {
        return this.pagedSupportWork(0, []).then(work => {
            return work;
        });
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedSupportWork(
        page: number,
        cumulativeResult: evidenceDto.SupportWork[]
    ): Promise<evidenceDto.SupportWork[]> {
        return ReportAjaxRepository.instance
            .findAllSupportWork(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedSupportWork(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class RiskWorkQueryDataSource
    extends BaseRelatedQueryDataSource<RiskWorkEvidenceDto>
    implements DataSource<RiskWorkEvidenceDto>
{
    // BaseWorkWithActions>
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<RiskWorkEvidenceDto>> {
        let riskWorkQ = this.riskWorkQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            riskWorkQ = riskWorkQ.then(riskWorks =>
                this.getRelatedReferralSummary(this.ctx, riskWorks)
            );
        }

        return riskWorkQ.then(riskWorks => new RiskWorkOnlyAnalysis(this.ctx, Lazy(riskWorks)));
    }

    private riskWorkQuery(): Promise<RiskWorkEvidenceDto[]> {
        return this.pagedRiskWork(0, []).then(work => {
            return work;
        });
    }

    /**
     * Recursive call to get risk work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedRiskWork(
        page: number,
        cumulativeResult: RiskWorkEvidenceDto[]
    ): Promise<RiskWorkEvidenceDto[]> {
        return ReportAjaxRepository.instance
            .findAllRiskWork(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedRiskWork(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 */
export class SupportRiskWorkQueryDataSource
    extends BaseRelatedQueryDataSource<BaseOutcomeBasedWork>
    implements DataSource<BaseOutcomeBasedWork>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        protected ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<BaseOutcomeBasedWork>> {
        let supportRiskWorkQ = this.supportRiskWorkQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            supportRiskWorkQ = supportRiskWorkQ.then(works =>
                this.getRelatedReferralSummary(this.ctx, works)
            );
        }

        return supportRiskWorkQ.then(works => new WorkAnyAnalysis(this.ctx, Lazy(works)));
    }

    private supportRiskWorkQuery(): Promise<evidenceDto.BaseOutcomeBasedWork[]> {
        return this.pagedSupportRiskWork(0, []).then(work => {
            return work;
        });
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedSupportRiskWork(
        page: number,
        cumulativeResult: BaseOutcomeBasedWork[]
    ): Promise<BaseOutcomeBasedWork[]> {
        return this.pagedSupportWork(page, cumulativeResult).then(allRisk =>
            this.pagedRiskWork(page, cumulativeResult).then(risk => {
                return allRisk.concat(risk);
            })
        );
    }

    private pagedSupportWork(
        page: number,
        cumulativeResult: BaseOutcomeBasedWork[]
    ): Promise<evidenceDto.BaseOutcomeBasedWork[]> {
        return ReportAjaxRepository.instance
            .findAllSupportWork(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedSupportWork(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
    private pagedRiskWork(
        page: number,
        cumulativeResult: BaseOutcomeBasedWork[]
    ): Promise<BaseOutcomeBasedWork[]> {
        return ReportAjaxRepository.instance
            .findAllRiskWork(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedRiskWork(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class RiskFlagsQueryDataSource
    extends BaseRelatedQueryDataSource<RiskFlags>
    implements DataSource<RiskFlags>
{
    // BaseWorkWithActions>
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<RiskFlags>> {
        let riskFlagsQ = this.riskFlagsQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            riskFlagsQ = riskFlagsQ.then(riskFlags =>
                this.getRelatedReferralSummary(this.ctx, riskFlags)
            );
        }

        if (this.currentSelectionCriteria.fetchClientDetail()) {
            riskFlagsQ = riskFlagsQ.then(riskFlags => this.getRelatedClients(riskFlags));
        }

        return riskFlagsQ.then(riskFlags => new RiskFlagsAnalysis(this.ctx, Lazy(riskFlags)));
    }

    private riskFlagsQuery(): Promise<RiskFlags[]> {
        return this.pagedRiskFlags(0, []).then(flags => {
            return flags;
        });
    }

    /**
     * Recursive call to get risk flags until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedRiskFlags(page: number, cumulativeResult: RiskFlags[]): Promise<RiskFlags[]> {
        return ReportAjaxRepository.instance
            .findAllRiskFlags(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedRiskFlags(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class SupportFlagsQueryDataSource
    extends BaseRelatedQueryDataSource<EvidenceFlags>
    implements DataSource<EvidenceFlags>
{
    // BaseWorkWithActions>
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<EvidenceFlags>> {
        let supportFlagsQ = this.supportFlagsQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            supportFlagsQ = supportFlagsQ.then(supportFlags =>
                this.getRelatedReferralSummary(this.ctx, supportFlags)
            );
        }

        if (this.currentSelectionCriteria.fetchClientDetail()) {
            supportFlagsQ = supportFlagsQ.then(supportFlags =>
                this.getRelatedClients(supportFlags)
            );
        }

        // NB we still need to include a questionnaire snapshot option here - see fetchQuestionnaireSnapshotAtEnd(),
        // although we have included the answers at the time of the flag change - which is much more accurate
        // it doesn't record all the answers at the time, only the changed answers - so we may want a snapshot
        // we therefore need to get a snapshot per srId? or for the whole referralStatus? - we choose the whole referralStatus
        if (this.currentSelectionCriteria.fetchQuestionnaireSnapshotAtEnd()) {
            // using the end SnapshotPeriod.DURING2 means still applying the start date of the period,
            // so by indicating 'fetch...End' we are implying we want to ignore the start
            // which we can do by fudging the criteria when loading the snapshots
            const reportOverride = {...this.currentReportCriteria, from: null};
            const snapshotsQ = QuestionnaireBaseSnapshotQueryDataSource.getData(
                this.ctx,
                this.currentSelectionCriteria,
                reportOverride,
                [SnapshotPeriod.DURING2]
            );
            supportFlagsQ = supportFlagsQ.then(result => {
                return snapshotsQ.then(snapshots => {
                    // could build a dictionary, but we just use filter
                    //const snapshotsBySrId =  new Dictionary<number, QuestionnaireAnswersSnapshotDto>();
                    //if (snapshotsBySrId.containsKey(s.serviceRecipientId))

                    result.forEach(flagEvFg => {
                        // NB we are a row per flag - where each row wants to show the work comment and the question/answers at the time
                        // but although the work comment at the time is already passed from EvidenceSupportFlagsToViewModel,
                        // the question/answer at the time would require a snapshot at the work date
                        // SO we choose to override the work comment to be the latest only - to match the latest question/answers data
                        // since the report is 'at end' already
                        // NB the alternative is to include snapshot data in the work item we retrieve

                        // get the latest workDate per serviceRecipientId
                        const latestWorkCommentForSrIdEvFlg = result
                            .filter(r => r.serviceRecipientId === flagEvFg.serviceRecipientId)
                            .reduce((prev, curr) => {
                                // comparisons will work with ISO
                                if (!prev || prev.workDate < curr.workDate) {
                                    return curr;
                                }
                                return prev;
                            });
                        const latestWorkCommentForSrId =
                            latestWorkCommentForSrIdEvFlg as SupportFlags;
                        const flag = flagEvFg as SupportFlags;
                        flag.latestWorkComment = latestWorkCommentForSrId?.work.comment;

                        const qnWork = snapshots
                            .filter(s => s.serviceRecipientId === flag.serviceRecipientId)
                            .map(s => {
                                const work: Partial<QuestionnaireWorkDto> = {
                                    answers: s.answers
                                };
                                return work as QuestionnaireWorkDto;
                            })
                            .pop();
                        if (qnWork) {
                            (flag as SupportFlags).questionnaireWork = qnWork;
                        }
                    });
                    return result;
                });
            });
        }

        return supportFlagsQ.then(
            supportFlags => new EvidenceFlagsAnalysis(this.ctx, Lazy(supportFlags))
        );
    }

    private supportFlagsQuery(): Promise<EvidenceFlags[]> {
        return this.pagedSupportFlags(0, []).then(flags => {
            return flags;
        });
    }

    /**
     * Recursive call to get support flags until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedSupportFlags(
        page: number,
        cumulativeResult: EvidenceFlags[]
    ): Promise<EvidenceFlags[]> {
        return ReportAjaxRepository.instance
            .findAllSupportFlags(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedSupportFlags(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class RiskRatingsQueryDataSource
    extends BaseRelatedQueryDataSource<RiskGroupEvidenceDto>
    implements DataSource<RiskGroupEvidenceDto>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<RiskGroupEvidenceDto>> {
        let riskRatingsQ = this.riskRatingQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            riskRatingsQ = riskRatingsQ.then(data =>
                this.getRelatedReferralSummary(this.ctx, data)
            );
        }

        return riskRatingsQ.then(data => new RiskRatingsAnalysis(this.ctx, Lazy(data)));
    }

    private riskRatingQuery(): Promise<RiskGroupEvidenceDto[]> {
        return this.pagedRiskRatings(0, []).then(data => {
            return data;
        });
    }

    /**
     * Recursive call to get risk raings until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedRiskRatings(
        page: number,
        cumulativeResult: RiskGroupEvidenceDto[]
    ): Promise<RiskGroupEvidenceDto[]> {
        return ReportAjaxRepository.instance
            .findAllRiskRatings(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedRiskRatings(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class ClientQueryDataSource implements DataSource<Client> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria,
        private previousDataSource: DataSource<number>
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<Client>> {
        let allClientsQ: Promise<Client[]> = this.previousDataSource
            ? this.clientsFromPreviousDataSource()
            : this.pagedClients(0, []);

        return allClientsQ
            .then(clients =>
                sequentialMapAll(clients, c => ClientQueryDataSource.getRelatedData(c))
            )
            .then(clients => {
                let uniqClients = Lazy(clients).uniq(c => c.clientId!);
                return new ClientAnalysis(this.ctx, uniqClients);
            });
    }

    private clientsFromPreviousDataSource(): Promise<Client[]> {
        let repoMethod: (ids: number[]) => Promise<Client[]>;
        // NB repoMethod is put through .chunk() below
        switch (this.currentSelectionCriteria.getDto().selectionCriteriaSource?.entityIdType) {
            case "id":
                repoMethod = ids => clientRepository.findAllClientsByClientId(ids);
                break;
            case "serviceRecipientId":
                repoMethod = srIds => clientRepository.findAllClientsByServiceRecipientId(srIds);
                break;
            default:
                throw new Error(
                    "this dataSource does not support the entityIdType: " +
                        this.currentSelectionCriteria.getDto().selectionCriteriaSource?.entityIdType
                );
        }

        const chunkedIdsQ = this.previousDataSource.getData().then(seqAnalysis => {
            return seqAnalysis.getData().chunk(50).toArray();
        });
        return chunkedIdsQ.then(ids => {
            return sequentialMapAll(ids, ids => repoMethod(ids)).then(all =>
                all.reduce((r, x) => r.concat(x), [])
            ); // flatMap
        });
    }

    private pagedClients(page: number, cumulativeResult: Client[]): Promise<Client[]> {
        return ReportAjaxRepository.instance
            .findAllClients(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedClients(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private static getRelatedData(client: Client): Promise<Client> {
        return Promise.resolve(client);
    }
}

/**
 * An independent class (ie not part of ReferralAggregate)
 */
export class ServiceRecipientAssociatedContactDataSource
    implements DataSource<ServiceRecipientAssociatedContact>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<ServiceRecipientAssociatedContact>> {
        return this.associatedContactQuery(this.ctx.getSessionData()).then(
            contacts => new AssociatedContactsAnalysis(this.ctx, Lazy(contacts))
        );
    }

    private associatedContactQuery(
        sessionData: SessionData
    ): Promise<ServiceRecipientAssociatedContact[]> {
        return this.pagedAssociatedContact(0, [], sessionData).then(work => {
            return work;
        });
    }

    /**
     * Recursive call to get activities until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     * @param sessionData
     */
    private pagedAssociatedContact(
        page: number,
        cumulativeResult: ServiceRecipientAssociatedContact[],
        sessionData: SessionData
    ): Promise<ServiceRecipientAssociatedContact[]> {
        return ReportAjaxRepository.instance
            .findAllAssociatedContacts(this.currentReportCriteria, page)
            .then(results => {
                if (results.length) {
                    return this.pagedAssociatedContact(
                        page + 1,
                        cumulativeResult.concat(results),
                        sessionData
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getRelatedReferralSummary(
        attendances: ClientAttendance[]
    ): Promise<ClientAttendance[]> {
        const bySrId = groupByNativeNumber(
            attendances,
            attendance => attendance.getDto().serviceRecipientId
        );
        const chunkedIds = Lazy(Object.keys(bySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            referralRepository.findAllReferralSummaryByServiceRecipientId(ids).then(refSums =>
                refSums.forEach(refSum => {
                    bySrId[refSum.serviceRecipientId].forEach(entity => {
                        entity.getDto().referralSummary = refSum;
                    });
                })
            );

        return sequentialMapAll(chunkedIds, singlePromise).then(() => attendances);
    }
}

export class AgencyQueryDataSource implements DataSource<Agency> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<Agency>> {
        return this.agencyQuery().then(agency => {
            return new AgencyAnalysis(this.ctx, Lazy(agency));
        });
    }

    private agencyQuery(): Promise<Agency[]> {
        return this.pagedAgencies(0, []).then(agencies => {
            return agencies;
        });
    }

    /**
     * Recursive call to get support work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedAgencies(page: number, cumulativeResult: Agency[]): Promise<Agency[]> {
        return ReportAjaxRepository.instance
            .findAllAgencies(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedAgencies(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    // TODO if referrerIndividualId contactsRepository.findAllIndividualsByCompanyId (requires dto changes)
    //private getRelatedData(client: Client): Promise<Client> {
    //     var clientQ: Promise<Client> = Promise.resolve(client);
    //     return clientQ;
}

/**
 * Get all the professionals, and all agencies
 * NB Could have used contactsRepository.findAllIndividualsByCompanyId (requires dto changes)
 */
export class ProfessionalQueryDataSource implements DataSource<Individual> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<Individual>> {
        return this.individualQuery().then(profs =>
            this.agencyQuery().then(agencies => {
                const agencyById: NumberToObjectMap<Agency> = {};
                agencies.forEach(a => {
                    agencyById[a.contactId] = a;
                });
                profs.forEach(p => {
                    if (p.organisationId) {
                        p.organisation = agencyById[p.organisationId];
                    }
                });

                // convert agencies without professionals into individuals with agency, so can utilise the same table
                const agencyIdsLoaded = new Set<number>();
                profs.forEach(p => {
                    agencyIdsLoaded.add(p.organisationId!);
                });
                const agenciesWithoutProfsAsIndividuals = agencies
                    .filter(a => !agencyIdsLoaded.has(a.contactId))
                    .map(a => {
                        const i: Individual = {} as Individual;
                        i.organisation = a;
                        return i;
                    });

                profs.push(...agenciesWithoutProfsAsIndividuals);
                // NB sorting is overridden by the table column order
                //profs.sort((a, b) => a.organisation.companyName.localeCompare(b.organisation.companyName, undefined, {sensitivity: 'base'}))
                return new ProfessionalAnalysis(this.ctx, Lazy(profs));
            })
        );
    }

    private individualQuery(): Promise<Individual[]> {
        return this.pagedIndividuals(0, []).then(profs => {
            return profs;
        });
    }

    private agencyQuery(): Promise<Agency[]> {
        return this.pagedAgencies(0, []).then(agencies => {
            return agencies;
        });
    }

    private pagedIndividuals(page: number, cumulativeResult: Individual[]): Promise<Individual[]> {
        return ReportAjaxRepository.instance
            .findAllProfessionals(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedIndividuals(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private pagedAgencies(page: number, cumulativeResult: Agency[]): Promise<Agency[]> {
        return ReportAjaxRepository.instance
            .findAllAgencies(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedAgencies(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class TaskStatusQueryDataSource implements DataSource<TaskStatus> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        protected ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<TaskStatus>> {
        return this.taskQuery()
            .then(
                tasks => sequentialMapAll(tasks, t => this.getRelatedData(t)) // TODO: batch tasks up and make getRelatedData take an array
            )
            .then(tasks => {
                return new TaskAnalysis(this.ctx, Lazy(tasks));
            });
    }

    private taskQuery(): Promise<TaskStatus[]> {
        return this.pagedTasks(0, []).then(tasks => {
            return tasks;
        });
    }

    /**
     * Recursive call until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedTasks(page: number, cumulativeResult: TaskStatus[]): Promise<TaskStatus[]> {
        return ReportAjaxRepository.instance
            .findAllTaskStatuses(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedTasks(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getRelatedData(taskStatus: TaskStatus): Promise<TaskStatus> {
        let rQ = Promise.resolve(taskStatus);

        if (this.currentSelectionCriteria.fetchTaskAudit()) {
            rQ = referralRepository
                .findLatestCommandPerTaskName(taskStatus.serviceRecipientId)
                .then(cmds => {
                    taskStatus.lastAudit = cmds
                        .filter(c => c.taskName == taskStatus.taskDefName)
                        .pop();
                    return taskStatus;
                });
        }

        return rQ.then(ts =>
            referralRepository
                .findOneReferralSummaryByServiceRecipientIdUsingDto(taskStatus.serviceRecipientId)
                .then(referralSummary => {
                    // the more useful aspects to display
                    taskStatus.referralSummary = referralSummary;
                    return taskStatus;
                })
        );
    }
}

export class BuildingQueryDataSource implements DataSource<Building> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<Building>> {
        return this.buildingQuery().then(bldg => {
            return new BuildingOnlyAnalysis(this.ctx, Lazy(bldg));
        });
    }

    private buildingQuery(): Promise<Building[]> {
        return this.pagedBldg(0, []);
    }

    /**
     * Recursive call to get risk work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedBldg(page: number, cumulativeResult: Building[]): Promise<Building[]> {
        return ReportAjaxRepository.instance
            .findAllBuildings(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedBldg(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class RepairQueryDataSource implements DataSource<RepairDto> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<RepairDto>> {
        return this.repairQuery().then(chg => {
            return new RepairOnlyAnalysis(this.ctx, Lazy(chg));
        });
    }

    private repairQuery(): Promise<RepairDto[]> {
        let q = this.pagedRepair(0, []);

        if (this.ctx.hasColumnStart("worker")) {
            q = q.then(repairs => this.getRelatedWorkers(repairs));
        }

        // gets the latest custom form work
        // NB form work tends to always mean the latest snapshot - but possibly should be the latest at that point in time
        if (this.currentSelectionCriteria.fetchCustomFormWork()) {
            q = q.then(repairs => this.getRelatedCustomForms(repairs));
        }

        return q;
    }

    private pagedRepair(page: number, cumulativeResult: RepairDto[]): Promise<RepairDto[]> {
        return ReportAjaxRepository.instance
            .findAllRepairs(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedRepair(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getRelatedWorkers(repairs: RepairDto[]): Promise<RepairDto[]> {
        const rwBySwId = groupByNativeNumber(repairs, r => r.supportWorkerId);
        const chunkedIds = Lazy(Object.keys(rwBySwId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            contactRepository.findAllContactsByContactId(ids).then(contacts => {
                return contacts.forEach(c => {
                    rwBySwId[c.contactId].forEach(r => {
                        const ind = <Individual>c;
                        r.supportWorkerDisplayName = [ind.firstName, ind.lastName].join(" ");
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => repairs);
    }

    private getRelatedCustomForms(repairs: RepairDto[]): Promise<RepairDto[]> {
        const evidenceGroupStr = this.currentSelectionCriteria.getDto().customFormEvidenceGroup!;
        const evidenceGroup = EvidenceGroup.fromName(evidenceGroupStr);

        const bySrId = groupByNativeNumber(repairs, r => r.serviceRecipientId);
        const singlePromise = (srId: number) =>
            getFormEvidenceRepository()
                .findLatestFormEvidenceSnapshotByServiceRecipientId(srId, evidenceGroup)
                .then(work => {
                    bySrId[srId].forEach(r => {
                        r.customFormWorkLatest = work;
                    });
                });
        const ids = repairs.map(r => r.serviceRecipientId);
        return sequentialMapAll(ids, singlePromise).then(() => repairs);
    }
}

export class FinanceChargeQueryDataSource implements DataSource<FinanceChargeDto> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<FinanceChargeDto>> {
        return this.chargeQuery().then(chg => {
            if (this.currentSelectionCriteria.fetchFinanceReceipts()) {
                return this.receiptQuery().then(rec => {
                    return new FinanceChargeAnalysis(
                        this.ctx,
                        financeCombined(chg, rec, this.ctx.getSessionData())
                    );
                });
            } else {
                return new FinanceChargeAnalysis(this.ctx, Lazy(chg));
            }
        });
    }

    private chargeQuery(): Promise<FinanceChargeDto[]> {
        var financeQ = this.pagedCharge(0, []);

        if (this.ctx.hasColumnStart("sr: ")) {
            financeQ = financeQ.then(charges => this.getAdditionalServiceRecipients(charges));
        }
        if (this.ctx.hasColumnStart("b: ")) {
            financeQ = financeQ.then(charges => this.getAdditionalBuildings(charges));
        }

        return financeQ;
    }

    private pagedCharge(
        page: number,
        cumulativeResult: FinanceChargeDto[]
    ): Promise<FinanceChargeDto[]> {
        return ReportAjaxRepository.instance
            .findAllFinanceCharges(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedCharge(
                        page + 1,
                        cumulativeResult.concat(result.map(r => r as FinanceChargeDto))
                    );
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getAdditionalReferrals<
        T extends {serviceRecipientId: number; referralSummary?: ReferralSummaryDto | undefined}
    >(charges: T[]): Promise<T[]> {
        const chgBySrId = groupByNativeNumber(charges, c => c.serviceRecipientId);
        const chunkedIds = Lazy(Object.keys(chgBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            referralRepository.findAllReferralSummaryByServiceRecipientId(ids).then(referrals => {
                return referrals.forEach(r => {
                    chgBySrId[r.serviceRecipientId].forEach(chg => {
                        chg.referralSummary = r;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => charges);
    }

    private getAdditionalServiceRecipients<
        T extends {serviceRecipientId: number; serviceRecipient?: ServiceRecipient | undefined}
    >(charges: T[]): Promise<T[]> {
        const chgBySrId = groupByNativeNumber(charges, c => c.serviceRecipientId);
        const chunkedIds = Lazy(Object.keys(chgBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            serviceRecipientRepository.findManyServiceRecipientByIds(ids).then(srs => {
                return srs.forEach(sr => {
                    chgBySrId[sr.serviceRecipientId].forEach(chg => {
                        chg.serviceRecipient = sr;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => charges);
    }

    private getAdditionalBuildings(charges: FinanceChargeDto[]): Promise<FinanceChargeDto[]> {
        const chgByBlgId = groupByNativeNumber(charges, c => c.buildingId);
        const chunkedIds = Lazy(Object.keys(chgByBlgId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            getBuildingRepository()
                .findAllBuildingsInIds(ids)
                .then(bldgs => {
                    return bldgs.forEach(b => {
                        chgByBlgId[b.buildingId].forEach(chg => {
                            chg.building = b;
                        });
                    });
                });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => charges);
    }

    private receiptQuery(): Promise<FinanceReceiptDto[]> {
        var receiptQ = this.pagedReceipt(0, []);

        if (this.ctx.hasColumnStart("r: ")) {
            receiptQ = receiptQ.then(receipts => this.getAdditionalReferrals(receipts));
        }

        return receiptQ;
    }

    private pagedReceipt(
        page: number,
        cumulativeResult: FinanceReceiptDto[]
    ): Promise<FinanceReceiptDto[]> {
        return ReportAjaxRepository.instance
            .findAllFinanceReceipts(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReceipt(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }
}

export class FinanceReceiptQueryDataSource implements DataSource<FinanceReceiptDto> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<FinanceReceiptDto>> {
        return this.chargeQuery().then(chg => {
            return new FinanceReceiptOnlyAnalysis(this.ctx, Lazy(chg));
        });
    }

    private chargeQuery(): Promise<FinanceReceiptDto[]> {
        let financeQ = this.pagedReceipt(0, []);

        if (this.ctx.hasColumnStart("r: ")) {
            financeQ = financeQ.then(charges => this.getAdditionalReferrals(charges));
        }

        return financeQ;
    }

    private pagedReceipt(
        page: number,
        cumulativeResult: FinanceReceiptDto[]
    ): Promise<FinanceReceiptDto[]> {
        return ReportAjaxRepository.instance
            .findAllFinanceReceipts(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReceipt(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getAdditionalReferrals(receipts: FinanceReceiptDto[]): Promise<FinanceReceiptDto[]> {
        const recBySrId = groupByNativeNumber(receipts, c => c.serviceRecipientId);
        const chunkedIds = Lazy(Object.keys(recBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            referralRepository.findAllReferralSummaryByServiceRecipientId(ids).then(referrals => {
                return referrals.forEach(r => {
                    recBySrId[r.serviceRecipientId].forEach(chg => {
                        chg.referralSummary = r;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => receipts);
    }
}

export class UserQueryDataSource implements DataSource<User> {
    // BaseWorkWithActions>
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<User>> {
        return this.usersQuery().then(users => {
            return new UserOnlyAnalysis(this.ctx, Lazy(users));
        });
    }

    private usersQuery(): Promise<User[]> {
        let usersQ = this.pagedUsers(0, []);

        if (this.currentSelectionCriteria.fetchAclPermissions()) {
            usersQ = usersQ.then(users => this.getAclPermissions(users));
        }

        return usersQ;
    }

    /**
     * Recursive call to get risk work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedUsers(page: number, cumulativeResult: User[]): Promise<User[]> {
        return ReportAjaxRepository.instance
            .findAllUsers(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedUsers(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getAclPermissions(users: User[]): Promise<User[]> {
        const byUserName = groupByNativeString(users, u => u.username);
        const ids = Object.keys(byUserName);
        const singlePromise = (username: string) =>
            aclRepository.findByUsername(username).then(userAcls =>
                byUserName[username].forEach(u => {
                    u.acls = userAcls;
                })
            );
        return sequentialMapAll(ids, singlePromise).then(() => users);
    }
}

export class ServiceTypeQueryDataSource implements DataSource<ServiceType> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<ServiceType>> {
        return sessionDataRepository.getSessionData().then(config => {
            return new ServiceTypeAnalysis(this.ctx, Lazy(config.getServiceTypes()));
        });
    }
}

export class ReviewQueryDataSource
    extends BaseRelatedQueryDataSource<Review>
    implements DataSource<Review>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<Review>> {
        let reviewsQ = this.reviewsQuery();

        if (this.currentSelectionCriteria.fetchReferral()) {
            reviewsQ = reviewsQ.then(reviews => this.getRelatedReferralSummary(this.ctx, reviews));
            reviewsQ = reviewsQ.then(reviews => this.getRelatedWorker(reviews));
        }

        return reviewsQ.then(reviews => new ReviewAnalysis(this.ctx, Lazy(reviews)));
    }

    private reviewsQuery(): Promise<Review[]> {
        return this.pagedReviews(0, []).then(reviews => {
            return reviews;
        });
    }

    /**
     * Recursive call to get risk work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedReviews(page: number, cumulativeResult: Review[]): Promise<Review[]> {
        return ReportAjaxRepository.instance
            .findAllReviews(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReviews(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    // result.supportWorkerDisplayName = contact.firstName + " " + contact.lastName;
    private getRelatedWorker(reviews: Review[]): Promise<Review[]> {
        const rwBySwId = groupByNativeNumber(
            reviews,
            review => review.referralSummary?.supportWorkerId
        );
        const chunkedIds = Lazy(Object.keys(rwBySwId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            contactRepository.findAllContactsByContactId(ids).then(contacts => {
                return contacts.forEach(c => {
                    rwBySwId[c.contactId].forEach(review => {
                        const ind = <Individual>c;
                        review.referralSummary!.supportWorkerDisplayName = [
                            ind.firstName,
                            ind.lastName
                        ].join(" ");
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => reviews);
    }
}


export class OccupancyQueryDataSource implements DataSource<OccupancyDto> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<OccupancyDto>> {
        return this.occupancyQuery().then(chg => {
            return new OccupancyAnalysis(this.ctx, Lazy(chg));
        });
    }

    private occupancyQuery(): Promise<OccupancyDto[]> {
        let occupyQ = this.pagedOccupy(0, []);

        if (this.ctx.hasColumnStart("b: ")) {
            occupyQ = occupyQ.then(data =>
                OccupancyQueryDataSource.getAdditionalBuildings(data, getBuildingRepository())
            );
        }
        if (this.ctx.hasColumnStart("sr: ")) {
            occupyQ = occupyQ.then(data =>
                OccupancyQueryDataSource.getAdditionalServiceRecipients(
                    data,
                    serviceRecipientRepository
                )
            );
        }

        return occupyQ;
    }

    private pagedOccupy(page: number, cumulativeResult: OccupancyDto[]): Promise<OccupancyDto[]> {
        return ReportAjaxRepository.instance
            .findAllOccupancy(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedOccupy(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private static getAdditionalReferrals<
        T extends {serviceRecipientId: number; referralSummary?: ReferralSummaryDto}
    >(data: T[], referralRepository: ReferralRepository): Promise<T[]> {
        const recBySrId = groupByNativeNumber(data, c => c.serviceRecipientId);
        const chunkedIds = Lazy(Object.keys(recBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            referralRepository.findAllReferralSummaryByServiceRecipientId(ids).then(referrals => {
                return referrals.forEach(r => {
                    recBySrId[r.serviceRecipientId].forEach(chg => {
                        chg.referralSummary = r;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => data);
    }

    private static getAdditionalServiceRecipients<
        T extends {serviceRecipientId: number; serviceRecipient?: ServiceRecipient}
    >(data: T[], serviceRecipientRepository: ServiceRecipientRepository): Promise<T[]> {
        const recBySrId = groupByNativeNumber(data, c => c.serviceRecipientId);
        const chunkedIds = Lazy(Object.keys(recBySrId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            serviceRecipientRepository.findManyServiceRecipientByIds(ids).then(srs => {
                return srs.forEach(sr => {
                    recBySrId[sr.serviceRecipientId].forEach(chg => {
                        chg.serviceRecipient = sr;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => data);
    }

    private static getAdditionalBuildings<T extends {buildingId?: number; building?: Building}>(
        data: T[],
        buildingRepository: BuildingRepository
    ): Promise<T[]> {
        const recByBId = groupByNativeNumber(data, c => c.buildingId);
        const chunkedIds = Lazy(Object.keys(recByBId))
            .filter(isNumber)
            .map(s => parseInt(s))
            .chunk(10)
            .toArray();
        const singlePromise = (ids: number[]) =>
            buildingRepository.findAllBuildingsInIds(ids).then(bldgs => {
                return bldgs.forEach(b => {
                    recByBId[b.buildingId].forEach(d => {
                        d.building = b;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => data);
    }
}


export class AddressHistoryQueryDataSource
    extends BaseRelatedQueryDataSource<AddressHistoryDto>
    implements DataSource<AddressHistoryDto>
{
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        super();
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    public getData(): Promise<SequenceAnalysis<AddressHistoryDto>> {
        let historyQ = this.addressHistoryQuery();

        historyQ = historyQ.then(history => this.getRelatedAddress(history));

        if (this.currentSelectionCriteria.fetchReferral()) {
            historyQ = historyQ.then(history => this.getRelatedReferralSummary(this.ctx, history));
        }

        return historyQ.then(history => new AddressHistoryAnalysis(this.ctx, Lazy(history)));
    }

    private addressHistoryQuery(): Promise<AddressHistoryDto[]> {
        return this.pagedAddress(0, []).then(history => {
            return history;
        });
    }

    /**
     * Recursive call to get risk work until nothing is brought back.
     * @param page zero-based page number
     * @param cumulativeResult
     */
    private pagedAddress(
        page: number,
        cumulativeResult: AddressHistoryDto[]
    ): Promise<AddressHistoryDto[]> {
        return ReportAjaxRepository.instance
            .findAllAddressHistory(this.currentReportCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedAddress(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getRelatedAddress(history: AddressHistoryDto[]): Promise<AddressHistoryDto[]> {
        const histByAdrId = groupByNativeNumber(history, item => item.addressId!);
        const chunkedIds = Lazy(Object.keys(histByAdrId))
            .filter(isNumber)
            .map(str => Number.parseInt(str))
            .chunk(25)
            .toArray();
        const singlePromise = (ids: number[]) =>
            addressRepository.findAllAddressByIds(ids).then(addresses => {
                return addresses.forEach(adr => {
                    histByAdrId[adr.addressId].forEach(adrhist => {
                        adrhist.address = adr;
                    });
                });
            });
        return sequentialMapAll(chunkedIds, singlePromise).then(() => history);
    }
}

export class EventQueryDataSource implements DataSource<CalendarEventWithParent> {
    private currentReportCriteria: ReportCriteriaDto;

    constructor(
        private ctx: AnalysisContext,
        protected currentSelectionCriteria: SelectionCriteria
    ) {
        this.currentReportCriteria = currentSelectionCriteria.getReportCriteriaDto();
    }

    // not related, we need to get a list of referrals to then work out how to load the events
    protected pagedReferralSummary(
        page: number,
        cumulativeResult: ReferralDto[]
    ): Promise<ReferralDto[]> {
        const liveCriteria = {...this.currentReportCriteria, referralStatus: "liveAtEnd"};
        return ReportAjaxRepository.instance
            .findAllReferralSummary(liveCriteria, page)
            .then(result => {
                if (result.length) {
                    return this.pagedReferralSummary(page + 1, cumulativeResult.concat(result));
                } else {
                    return Promise.resolve(cumulativeResult);
                }
            });
    }

    private getEvents(referrals: ReferralDto[]): Promise<CalendarEventWithParent[]> {
        // this is actually a SINGLE referral, to ensure we know what we are loading
        const singlePromise = (id: number) => {
            const toContactId = referrals
                .filter(r => r.referralId == id)
                .map(r => r.contactId)
                .pop();
            // NB to obtain the right referral, this does come at a cost of duplicate loading for referrals with the same contactId
            const p = toContactId
                ? calendarRepository.fetchCalendarsByContactIds(
                      [toContactId],
                      EccoDate.parseIso8601(this.currentReportCriteria.from)!,
                      EccoDate.parseIso8601(this.currentReportCriteria.to)!
                  )
                : Promise.resolve([]);
            return p.then(events => {
                const r = referrals.filter(r => r.referralId == id).pop()!;
                return events.map(e => {
                    const eParent = e as CalendarEventWithParent;
                    eParent.parent = {referral: r};
                    return eParent;
                });
            });
        };

        const rIds = referrals.map(r => r.referralId);
        return sequentialMapAll(rIds, singlePromise).then(all =>
            all.reduce((r: CalendarEventWithParent[], x) => r.concat(x), [])
        ); // flatMap
    }

    public getData(): Promise<SequenceAnalysis<CalendarEventWithParent>> {
        let referralsQ = this.pagedReferralSummary(0, []);
        let eventsQ = referralsQ.then(refs => this.getEvents(refs));
        return eventsQ.then(events => new CalendarAnalysis(this.ctx, Lazy(events)));
    }
}
