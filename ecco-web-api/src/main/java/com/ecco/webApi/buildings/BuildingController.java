package com.ecco.webApi.buildings;

import com.ecco.buildings.dom.FixedContainer;
import com.ecco.buildings.viewModel.BuildingViewModel;
import com.ecco.infrastructure.annotations.ReadOnlyTransaction;
import com.ecco.infrastructure.config.root.CacheConfig;
import com.ecco.service.BuildingService;
import com.ecco.webApi.controllers.BaseWebApiController;
import com.ecco.webApi.support.EtaggedResponseCacheManager;
import com.ecco.webApi.viewModels.Result;
import lombok.AllArgsConstructor;
import org.springframework.composed.web.rest.json.GetJson;
import org.springframework.composed.web.rest.json.PostJson;
import org.springframework.security.access.annotation.Secured;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;

import org.jspecify.annotations.NonNull;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ecco.rota.service.BuildingCareRunRotaHandler.CARERUN_RESOURCE_ID;
import static java.util.stream.Collectors.toList;

@RestController
@PreAuthorize("hasRole('ROLE_STAFF')")
@AllArgsConstructor
public class BuildingController extends BaseWebApiController {

    static private Predicate<FixedContainer> isNotCareRun = it -> !it.getResourceTypeId().equals(CARERUN_RESOURCE_ID);

    private final BuildingService buildingService;
    private final FixedContainerViewModelAdapter fixedContainerViewModelAdapter;

    @NonNull
    private final BuildingCommandHandler buildingCommandHandler;

    @NonNull
    private final AddressLocationChangeCommandHandler addressCommandHandler;

    private final EtaggedResponseCacheManager etaggedResponseCacheManager;

    // get the id - with hateos the whole URL is the id
    // but for the purposes of retrofitting, getting the id like this is acceptable
    // NB see https://github.com/spring-projects/spring-hateoas/issues/66
    // NB and http://tommyziegler.com/how-to-expose-the-resourceid-with-spring-data-rest/
    public static Pattern extractIdPattern = Pattern.compile("buildings/(\\d+)/");
    public static final Function<String, Integer> EXTRACT_ID_FN = (href) -> {
        Matcher matches = extractIdPattern.matcher(href);
        matches.find();
        String id = matches.group(1);
        return Integer.parseInt(id);
    };

    @GetJson("/buildings/{id}/")
    @Transactional(readOnly = true, propagation = Propagation.REQUIRED, value = "transactionManager")
    public FixedContainerViewModel findOne(@PathVariable int id) {
        BuildingViewModel buildingViewModel = buildingService.findBuildingViewModelById(id);
        return buildingViewModel != null ? fixedContainerViewModelAdapter.toModel(buildingViewModel) : null;
    }

    @ReadOnlyTransaction
    @GetJson("/buildings/byServiceRecipient/{serviceRecipientId}/")
    public FixedContainerViewModel findOneBySrId(@PathVariable int serviceRecipientId) {
        BuildingViewModel buildingViewModel = buildingService.findBuildingViewModelByServiceRecipientId(serviceRecipientId);
        return buildingViewModel != null ? fixedContainerViewModelAdapter.toModel(buildingViewModel) : null;
    }

    @GetJson("/buildings/byExternalRef/{ref}/")
    public FixedContainerViewModel findOneByRef(@PathVariable String ref) {
        BuildingViewModel buildingViewModel = buildingService.findBuildingViewModelByExternalRef(ref);
        return buildingViewModel != null ? fixedContainerViewModelAdapter.toModel(buildingViewModel) : null;
    }

    @Secured("ROLE_ADMINBUILDING") // TODO: Provide workflow for removal?
    @DeleteMapping("/buildings/{id}/")
    public Result deleteOne(@PathVariable int id) {
        buildingService.deleteBuilding(id);
        return new Result("deleted");
    }

    /**
     * @param resourceType defaults to BUILDING resource type when omitted
     * @param showChildren defaults to true.  Include rooms/runs etc at this location
     * @param addressLocationId if specified, finds all at this location (e.g. rooms within a care home)
     */
    @ReadOnlyTransaction
    @GetJson("/buildings/")
    public void findFixedContainers(
            WebRequest request,
            HttpServletResponse response,
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @RequestParam(required=false) Integer addressLocationId) throws IOException {

        var key = resourceType == null ? "all" : resourceType;
        key = key + (showChildren ? "withChildren" : "noChildren");
        key = key + (showDisabled ? "withDisabled" : "");

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        etaggedResponseCacheManager.getFromCacheWithEtagHandling(request, response,
                CacheConfig.CACHE_BUILDINGS, key, 300,
                () -> buildingService.findBuildingViewModelsWithFilters(showChildren, showDisabled, resourceTypes, addressLocationId != null ? Collections.singletonList(addressLocationId) : null, null)
                        .stream()
                        .map(fixedContainerViewModelAdapter::toModel)
                        .collect(toList()));
    }

    @ReadOnlyTransaction
    @GetJson("/buildings/byIds/")
    public Iterable<FixedContainerViewModel> findAllByIds(@RequestParam(name = "ids") List<Integer> buildingIds) {
        return buildingService.findBuildingViewModelsByIds(buildingIds)
                .stream()
                .map(fixedContainerViewModelAdapter::toModel)
                .toList();
    }

    @ReadOnlyTransaction
    @GetJson("/buildings/byLocationIds/")
    public Iterable<FixedContainerViewModel> findByLocations(
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @RequestParam List<Integer> ids) {

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        return buildingService.findBuildingViewModelsWithFilters(showChildren, showDisabled, resourceTypes, ids, null)
                .stream()
                .map(fixedContainerViewModelAdapter::toModel)
                .collect(toList());
    }

    // UNUSED
    @ReadOnlyTransaction
    @GetJson("/buildings/byLocation/{addressLocationId}/")
    public Iterable<FixedContainerViewModel> findByLocation(
            @RequestParam(required=false) String resourceType,
            @RequestParam(required=false, defaultValue = "true") Boolean showChildren,
            @RequestParam(required=false, defaultValue = "false") Boolean showDisabled,
            @PathVariable int addressLocationId) {

        var resourceTypes = resourceType == null ? null : new String[]{resourceType};
        return buildingService.findBuildingViewModelsHierarchical(showChildren, showDisabled, resourceTypes, Collections.singletonList(addressLocationId))
                .stream()
                .map(fixedContainerViewModelAdapter::toModel)
                .collect(toList());
    }

    @ReadOnlyTransaction
    @GetJson("/buildings/{id}/children/")
    public Iterable<FixedContainerViewModel> findChildren(@PathVariable int id) {
        return buildingService.findChildBuildingViewModels(id)
                .stream()
                .map(fixedContainerViewModelAdapter::toModel)
                .collect(toList());
    }

    @ReadOnlyTransaction
    @GetJson("/buildings/{id}/careruns/")
    public Iterable<FixedContainerViewModel> findShifts(@PathVariable int id) {
        // show all careruns - whether disabled or not
        return buildingService.findCareRunViewModels(id, CARERUN_RESOURCE_ID)
                .stream()
                .map(fixedContainerViewModelAdapter::toModel)
                .collect(toList());
    }

    @PostJson("/buildings/commands/")
    public Result addBuilding(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return buildingCommandHandler.handleCommand(authentication, null, requestBody);
    }

    @PostJson("/buildings/address-location/commands/")
    public Result buildingAddress(
            @NonNull Authentication authentication,
            @NonNull @RequestBody String requestBody) throws IOException {

        return addressCommandHandler.handleCommand(authentication, null, requestBody);
    }

}
